/* Base layout and component styles; theme variables come from theme-*.css */

html, body {
  height: 100%;
  margin: 0;
  background: var(--bg);
  color: var(--text);
  font-family: Inter, Segoe UI, system-ui, Arial, sans-serif;
}

.app {
  display: grid;
  grid-template-columns: 64px 1fr 360px;
  grid-template-rows: 64px 1fr;
  gap: 0;
  height: 100vh;
  padding: 0;
}

.topbar {
  grid-column: 1 / -1;
  background: var(--card);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  border-radius: 0;
  box-shadow: none;
  height: 64px;
}

.logo { display:flex; gap:12px; align-items:center; }
.logo .dot {
  width: 36px; height: 36px; border-radius: 8px;
  background: var(--brand-gradient);
  display:flex; align-items:center; justify-content:center; font-weight:700;
  color: var(--brand-foreground);
}

.search { display:flex; align-items:center; background: var(--glass); padding:8px 12px; border-radius:10px; gap:8px; width: 640px; }
.search input { background: transparent; border: none; outline: none; color: inherit; font-size: 14px; width: 100%; }

.leftbar { background: var(--card); border-radius:0; padding:0; display:flex; flex-direction:column; gap:0; align-items:center; height:100vh; }
.icon { width: 40px; height: 40px; border-radius: 8px; display:flex; align-items:center; justify-content:center; cursor:pointer; color: var(--text); }
.icon.active { background: var(--brand-gradient); color: var(--brand-foreground); }

.center { background: var(--center-bg); border-radius:0; padding:16px; display:flex; flex-direction:column; gap:12px; overflow: hidden; }
.conversation { flex:1; overflow-y:auto; padding:8px; border-radius:8px; background: var(--conversation-bg); }
.message { display:flex; gap:12px; margin:8px 0; }
.user { margin-left:auto;  padding:10px 12px; border-radius:10px; max-width:70%; }
.ai { padding:12px; border-radius:10px; max-width:70%; }

.composer { display:flex; gap:12px; align-items:center; }
.composer input { flex: 1; padding:10px; border-radius:8px; border:1px solid var(--border-soft); background: transparent; color: inherit; }

.sidebar { background: var(--card); border-radius:0; padding:0; display:flex; flex-direction:column; gap:0; height:100vh; }
.context-section { background: var(--section-bg); padding:10px 16px; border-radius:0; }

.btn { background: var(--accent); border: none; padding: 8px 12px; border-radius: 8px; color: var(--btn-foreground); cursor: pointer; font-weight: 600; }
.btn.secondary { background: var(--btn-secondary-bg); color: var(--btn-secondary-fg); }

.muted { color: var(--muted); font-size: 13px; }
.small { font-size:12px; color: var(--muted); }

.action-card { background: var(--action-bg); border: 1px solid var(--border-soft); padding: 12px; border-radius: 10px; margin: 8px 0; }
.payload { background: var(--code-bg); padding: 10px; border-radius: 8px; font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, monospace; font-size: 13px; color: var(--code-fg); }

.flow-list { max-height: 140px; overflow: auto; }
.audit { max-height: 140px; overflow: auto; background: var(--audit-bg); padding: 8px; border-radius: 8px; }

.quick-actions { position: fixed; right: 28px; bottom: 28px; display:flex; flex-direction: column; gap: 8px; }
.qa-btn { background: var(--qa-gradient); border: none; padding: 10px; border-radius: 12px; color: var(--qa-fg); cursor: pointer; }

.modal { position: fixed; left:50%; top:50%; transform: translate(-50%, -50%); background: var(--card); padding: 16px; border-radius: 12px; width: 820px; max-width: 96%; box-shadow: 0 12px 48px var(--shadow-stronger); z-index: 80; }
.hidden { display: none; }

.table { width:100%; border-collapse: collapse; }
td, th { padding: 6px; border-bottom: 1px solid var(--border-soft); font-size: 13px; }

@media (max-width: 1100px) {
  .app { grid-template-columns: 56px 1fr; grid-template-rows: 64px 1fr 360px; }
  .sidebar { grid-column: 1 / -1; }
}
