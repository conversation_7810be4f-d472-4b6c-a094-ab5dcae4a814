<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>AI WMS 核心操作台 原型</title>
  <link id="theme-css" rel="stylesheet" href="./styles/theme-light.css" />
  <link rel="stylesheet" href="./styles/base.css" />
</head>
<body>
  <div class="app">
    <div class="topbar">
      <div class="logo"><div class="dot">AI</div><div><div style="font-weight:700">AI WMS 操作台</div><div class="small">自然语言驱动 · 可视确认 · 可审计</div></div></div>
      <div class="search">
        <svg width="18" viewBox="0 0 24 24" fill="none"><path stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" d="M21 21l-4.35-4.35"/><circle cx="11" cy="11" r="6" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/></svg>
        <input id="cmd-input" placeholder="按 Ctrl/Cmd+K 唤起命令，或直接输入：例如：出库给客户A 500件，优先近效期" />
        <div class="muted" style="font-size:12px">置信度：<span id="conf">—</span></div>
      </div>
      <div style="display:flex;gap:8px;align-items:center">
        <div class="small">角色: <strong>操作员</strong></div>
        <button id="open-flow" class="btn">流程编排</button>
        <button id="theme-toggle" class="btn secondary" title="切换亮/暗">切换主题</button>
      </div>
    </div>

    <div class="leftbar">
      <div class="icon active" title="仪表盘">🏷️</div>
      <div class="icon" title="波次">📦</div>
      <div class="icon" title="机器人">🤖</div>
      <div class="icon" title="打印机">🖨️</div>
      <div class="icon" title="告警">⚠️</div>
    </div>

    <div class="center">
      <div style="display:flex;gap:12px;align-items:center">
        <div style="flex:1">
          <div class="muted">对话 / 操作流</div>
          <div class="small">AI 解析你的自然语言并生成可执行的动作卡；确认后执行或 dry-run。</div>
        </div>
        <div style="display:flex;gap:8px">
          <button id="dry-run-toggle" class="btn">Dry-Run: ON</button>
          <button id="clear-log" class="btn secondary">清空</button>
        </div>
      </div>

      <div id="conversation" class="conversation" aria-live="polite"></div>

      <div class="composer">
        <input id="user-input" placeholder="也可以在这里输入指令并回车（示例：出库给客户B 200件，优先近效期）" />
        <button id="send-btn" class="btn">发送</button>
      </div>
    </div>

    <div class="sidebar">
      <div class="context-section">
        <div style="display:flex;justify-content:space-between;align-items:center"><div class="muted">上下文面板</div><div class="small">SOP / 仓位图 / 扫描图</div></div>
        <div style="display:flex;gap:8px;margin-top:8px">
          <div style="flex:1">
            <div style="font-weight:700">当前作业</div>
            <div class="small">作业ID: <span id="job-id">—</span></div>
            <div class="small">状态: <span id="job-status">空闲</span></div>
          </div>
          <div style="width:120px;text-align:right"><button id="inspect-sop" class="btn">检索SOP</button></div>
        </div>
      </div>

      <div class="context-section">
        <div style="display:flex;justify-content:space-between;align-items:center"><div class="muted">流程模板</div><div class="small">可保存/复用</div></div>
        <div class="flow-list" id="flows"></div>
        <div style="display:flex;gap:8px;margin-top:8px"><input id="flow-name" placeholder="流程名（示例：退货快速处理）" style="flex:1;padding:6px;border-radius:6px;background:transparent;border:1px solid rgba(255,255,255,0.04)"/><button id="save-flow" class="btn">保存</button></div>
      </div>

      <div class="context-section">
        <div class="muted">审计日志</div>
        <div class="audit" id="audit-log"></div>
      </div>

      <div style="display:flex;gap:8px;justify-content:space-between">
        <div class="small">模拟库存: <strong id="stock-count">1000</strong></div>
        <div class="small">模拟保质期商品: <strong>120</strong></div>
      </div>
    </div>

    <div class="quick-actions">
      <button class="qa-btn" id="start-pick">开始拣货</button>
      <button class="qa-btn" id="start-receive">开始收货</button>
      <button class="qa-btn" id="open-templates">模板中心</button>
    </div>

    <!-- Flow Composer Modal -->
    <div id="flow-modal" class="modal hidden">
      <h3>流程编排器（在线生成 / 编辑）</h3>
      <div style="display:flex;gap:12px;margin-top:8px">
        <div style="flex:1">
          <div class="small">流程草案（由 AI 从自然语言生成，可编辑）</div>
          <textarea id="flow-draft" style="width:100%;height:160px;padding:8px;border-radius:8px;background:transparent;border:1px solid var(--border-soft);color:inherit"></textarea>
        </div>
        <div style="width:240px">
          <div class="small">快速字段</div>
          <div style="display:flex;flex-direction:column;gap:8px;margin-top:8px">
            <input id="fld-supplier" placeholder="供应商" style="padding:8px;border-radius:8px;background:transparent;border:1px solid var(--border-soft)" />
            <input id="fld-sku" placeholder="SKU" style="padding:8px;border-radius:8px;background:transparent;border:1px solid var(--border-soft)" />
            <input id="fld-qty" placeholder="数量" style="padding:8px;border-radius:8px;background:transparent;border:1px solid var(--border-soft)" />
            <button id="apply-template" class="btn">插入到草案</button>
          </div>
        </div>
      </div>
      <div style="display:flex;gap:8px;justify-content:flex-end;margin-top:12px">
        <button id="publish-flow" class="btn">审批并发布</button>
  <button id="close-flow" class="btn secondary">关闭</button>
      </div>
    </div>

  </div>

  <script>
    // 引入通用 AI 服务（在本地直接引用 ai.js 文件）。
    // 如果你使用服务器，请把 <script src="./ai.js"></script> 放在本页之前加载。
  </script>
  <script src="./ai.js"></script>
  <script>
    // 尝试加载外部 JSON 配置（可选）。
    // 注意：直接用 file:// 打开可能被浏览器拦截跨源读取，建议起本地服务或将配置内联。
    (async () => {
      try {
        await AI.loadConfig('./ai-config.json');
        console.log('[AI] config loaded:', AI.getConfig());
      } catch (e) {
        console.warn('[AI] load config skipped or failed:', e?.message || e);
      }
    })();
    // 模拟数据和工具
    const conversation = document.getElementById('conversation');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const cmdInput = document.getElementById('cmd-input');
  const confEl = document.getElementById('conf');
  const themeLink = document.getElementById('theme-css');
    const dryToggle = document.getElementById('dry-run-toggle');
    const auditLog = document.getElementById('audit-log');
    const flowsEl = document.getElementById('flows');
    const flowModal = document.getElementById('flow-modal');
    const flowDraft = document.getElementById('flow-draft');
    const stockEl = document.getElementById('stock-count');
    const jobIdEl = document.getElementById('job-id');
    const jobStatusEl = document.getElementById('job-status');

    let dryRun = true;
    let stock = 1000;
    let flows = [];
    let audit = [];

    function appendUser(text){
      const el = document.createElement('div'); el.className='message';
      const b=document.createElement('div'); b.className='user'; b.textContent=text; el.appendChild(b); conversation.appendChild(el); conversation.scrollTop = conversation.scrollHeight;
    }
    function appendAI(html){
      const el = document.createElement('div'); el.className='message';
      const b=document.createElement('div'); b.className='ai'; b.innerHTML=html; el.appendChild(b); conversation.appendChild(el); conversation.scrollTop = conversation.scrollHeight;
    }

  function mockLLMParse(text){
      // 使用通用 AI 模块的轻量解析，保持与原返回一致
      const { intent, confidence, payload } = AI.mockParse(text);
      const qtyPreview = (payload.qty || (payload.items ? payload.items[0].qty : 0) || 0);
      const impactSign = (intent === 'create_inbound' || intent === 'create_return') ? '+' : (intent === 'create_outbound' ? '-' : '±');
      const actionHtml = `
        <div class="action-card">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <div style="font-weight:700">意图：${intent}</div>
              <div class="small">置信度：${(confidence*100).toFixed(0)}%</div>
            </div>
            <div style="display:flex;gap:8px">
              <button class="btn" onclick="confirmAction('${intent.replace(/[^a-z_]/g,'')}', ${confidence})">确认执行</button>
              <button class="btn secondary" onclick="editAction('${intent.replace(/[^a-z_]/g,'')}')">修改</button>
            </div>
          </div>
          <div style="margin-top:8px;display:flex;gap:12px">
            <div style="flex:1">
              <div class="small">建议动作（JSON 预览）</div>
              <pre class="payload">${JSON.stringify({intent,payload,confidence},null,2)}</pre>
            </div>
            <div style="width:220px">
              <div class="small">影响预估</div>
              <div style="margin-top:8px">库存变更: <strong>${impactSign} ${qtyPreview}</strong></div>
              <div style="margin-top:8px" class="small">风险: <span style="color:#ffd">中等</span></div>
              <div style="margin-top:12px"><label class="small">审批人：<select id="approver"><option>仓库主管</option><option>质检</option></select></label></div>
            </div>
          </div>
        </div>
      `;
      return {intent,confidence,payload,actionHtml};
    }

    function handleSend(text){
      appendUser(text);
      const parsed = mockLLMParse(text);
      confEl.textContent = (parsed.confidence*100).toFixed(0)+'%';
      // AI 回复 + action card
      appendAI(`<div style="font-weight:700">AI: 我理解为 — ${parsed.intent}</div><div class='small' style='margin-top:6px'>基于 SOP（示例）给出建议：</div>`+parsed.actionHtml);
      // add audit trace
      audit.unshift({time:new Date().toLocaleString(), user:text, intent:parsed.intent, payload:parsed.payload, dry:dryRun});
      renderAudit();
    }

    sendBtn.addEventListener('click', ()=>{ if(userInput.value.trim()) { handleSend(userInput.value.trim()); userInput.value=''; } });
    userInput.addEventListener('keydown', (e)=>{ if(e.key==='Enter'){ sendBtn.click(); } });

    // top command
    cmdInput.addEventListener('keydown', (e)=>{ if(e.key==='Enter'){ if(cmdInput.value.trim()){ handleSend(cmdInput.value.trim()); cmdInput.value=''; } } if((e.ctrlKey||e.metaKey) && e.key.toLowerCase()==='k'){ e.preventDefault(); userInput.focus(); }});

    // dry run toggle
    dryToggle.addEventListener('click', ()=>{ dryRun = !dryRun; dryToggle.textContent = 'Dry-Run: '+(dryRun? 'ON':'OFF'); dryToggle.style.background = dryRun? 'var(--accent)':'#ef4444'; });

    // confirm execution (exposed to global from action card)
    window.confirmAction = function(intent,confidence){
      const last = audit[0];
      const execId = 'EXEC-'+Date.now();
      jobIdEl.textContent = execId;
      jobStatusEl.textContent = '执行中';
      appendAI('<div style="font-weight:700">系统：</div><div class="small">开始'+(dryRun? '（Dry-Run）':'')+'执行 — '+intent+'</div>');
      setTimeout(()=>{
        // 模拟结果
        const qty = last && last.payload && (last.payload.qty || (last.payload.items? last.payload.items[0].qty:0)) || 0;
        const sign = (intent === 'create_inbound' || intent === 'create_return') ? 1 : (intent === 'create_outbound' ? -1 : 0);
        const delta = sign * qty;
        if(!dryRun){ stock = Math.max(0, stock + delta); stockEl.textContent = stock; }
        const resHtml = `<div style="font-weight:700">执行完成</div><div class="small">执行ID: ${execId}</div><div style="margin-top:8px">变更: ${dryRun? '模拟 - 未提交':'已提交'}，库存 ${delta>=0? '+':''}${delta}</div>`;
        appendAI(resHtml);
        jobStatusEl.textContent = dryRun? '已模拟' : '已完成';
        audit.unshift({time:new Date().toLocaleString(), user:'系统', intent:intent+'_executed', payload:{execId,qty,delta}, dry:dryRun});
        renderAudit();
      },900);
    }

    window.editAction = function(){
      // 打开流程编排 modal 并填入草案（修复：避免使用错误的转义序列，使用模板字面量）
      flowDraft.value = `【请在此编辑或审阅 AI 生成的流程步骤】

示例步骤：
1. 接收并核对数量
2. 视检并称重，如差异≤2%入库
3. 异常则分配给质检并通知`;
      flowModal.classList.remove('hidden');
    }

    document.getElementById('open-flow').addEventListener('click', ()=>{ flowModal.classList.remove('hidden'); });
    document.getElementById('close-flow').addEventListener('click', ()=>{ flowModal.classList.add('hidden'); });
  document.getElementById('publish-flow').addEventListener('click', ()=>{ const name = prompt('输入审批人 (示例: 仓库经理)'); if(name){ const fid='FLOW-'+Date.now(); flows.push({id:fid,name:'发布流程-'+fid,content:flowDraft.value}); renderFlows(); flowModal.classList.add('hidden'); alert('流程已发布并记录审批人：'+name);} });
    document.getElementById('apply-template').addEventListener('click', ()=>{ const s=`供货方:${document.getElementById('fld-supplier').value||'X'}, SKU:${document.getElementById('fld-sku').value||'A123'}, 数量:${document.getElementById('fld-qty').value||100}`; flowDraft.value = (flowDraft.value||'') + '\n' + s; });
    document.getElementById('save-flow').addEventListener('click', ()=>{ const n = document.getElementById('flow-name').value || ('流程-'+(flows.length+1)); const fid='FLOW-'+Date.now(); flows.push({id:fid,name:n,content:'由用户保存的流程模板'}); renderFlows(); document.getElementById('flow-name').value=''; });

  function renderFlows(){ flowsEl.innerHTML=''; flows.forEach(f=>{ const d=document.createElement('div'); d.style.padding='8px'; d.style.borderRadius='8px'; d.style.display='flex'; d.style.justifyContent='space-between'; d.style.alignItems='center'; d.style.gap='8px'; d.style.marginTop='6px'; d.style.background='var(--section-bg)'; d.innerHTML = `<div><div style="font-weight:700">${f.name}</div><div class="small">${f.id}</div></div><div style="display:flex;gap:8px"><button class="btn" onclick='applyFlow("${f.id}")'>应用</button><button class="btn secondary" onclick='deleteFlow("${f.id}")'>删除</button></div>`; flowsEl.appendChild(d); }); }

    window.applyFlow = function(id){ const f = flows.find(x=>x.id===id); if(!f) return alert('流程未找到'); appendUser('应用流程：'+f.name); appendAI('<div class="small">流程已生成 — 预览：</div><div class="action-card"><pre class="payload">'+(f.content||'空')+'</pre><div style="display:flex;gap:8px;margin-top:8px"><button class="btn" onclick="confirmAction(\'apply_flow\',0.9)">确认并执行</button></div></div>'); audit.unshift({time:new Date().toLocaleString(), user:'应用流程', intent:'apply_flow', payload:{flowId:id}, dry:dryRun}); renderAudit(); }
    window.deleteFlow = function(id){ flows = flows.filter(x=>x.id!==id); renderFlows(); }

    function renderAudit(){ auditLog.innerHTML=''; audit.slice(0,40).forEach(a=>{ const d = document.createElement('div'); d.style.padding='6px'; d.style.borderBottom='1px solid rgba(255,255,255,0.02)'; d.innerHTML = `<div style="font-weight:700">${a.intent}</div><div class="small">${a.time} · ${a.dry? 'dry-run':'exec'} · ${JSON.stringify(a.payload)}</div>`; auditLog.appendChild(d); }); }

    document.getElementById('inspect-sop').addEventListener('click', ()=>{ appendAI('<div style="font-weight:700">SOP 检索结果（示例）</div><div class="small">入库 SOP v2.1：优先称重与视检，阈值 2%。</div>'); });
    document.getElementById('start-pick').addEventListener('click', ()=>{ handleSend('开始拣货 300 件，优先近效期'); });
    document.getElementById('start-receive').addEventListener('click', ()=>{ handleSend('接收3托盘，供货方X'); });
    document.getElementById('open-templates').addEventListener('click', ()=>{ alert('模板中心（示例）：'+flows.length+' 个模板'); });
    document.getElementById('clear-log').addEventListener('click', ()=>{ conversation.innerHTML=''; });

    // theme toggle
    const themeToggleBtn = document.getElementById('theme-toggle');
    function setTheme(mode){
      themeLink.href = `./styles/theme-${mode}.css`;
      localStorage.setItem('theme', mode);
    }
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
    themeToggleBtn.addEventListener('click', ()=>{
      const current = themeLink.getAttribute('href').includes('theme-dark.css') ? 'dark' : 'light';
      setTheme(current === 'light' ? 'dark' : 'light');
    });

    // keyboard shortcut for command palette
    document.addEventListener('keydown',(e)=>{ if((e.ctrlKey||e.metaKey) && e.key.toLowerCase()==='k'){ e.preventDefault(); userInput.focus(); } });

    // 初始化
    stockEl.textContent = stock;
    renderFlows();
    renderAudit();
  </script>
</body>
</html>
