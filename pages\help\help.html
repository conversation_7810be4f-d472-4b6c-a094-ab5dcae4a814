<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 运维助手 - WMS 智能客服</title>
    <link id="theme-css" rel="stylesheet" href="../../styles/theme-light.css" />
    <link rel="stylesheet" href="../../public/css/tokens.css" />

    <link rel="stylesheet" href="../../styles/base.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet" href="../../components/conversation-list/conversation.css" />
    <link rel="stylesheet" href="./help.css" />

    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sql.js@1.10.2/dist/sql-wasm.js"></script>









</head>
<body>
    <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-top sidebar-top-row">
                <div class="brand-row">
                    <div class="brand-text-col">
                        <span class="brand-title">AI 运维助手</span>
                        <span class="brand-subtitle">WMS Support</span>
                    </div>
                </div>
                <div>
                    <button id="new-chat-btn-top" class="new-chat-btn new-chat-btn--top" title="开启新对话">➕ 新建对话</button>
                </div>
            </div>
            <div class="sidebar-mid">
                <div class="sidebar-search" id="sidebar-search" title="搜索对话">
                    <input id="conversation-search" type="text" placeholder="搜索" />
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>
                </div>
                <div class="sidebar-header">
                    <span class="sidebar-section-title">最近对话</span>
                </div>
                <div id="conversation-list" class="conversation-list">
                    <div class="empty-conv-hint">暂无对话，点击上方新建</div>
                </div>
            </div>
            <div class="sidebar-bottom">
                <button id="settings-btn" title="设置">
                    ⚙ 设置
                </button>
                <div class="version-label">v0.1.0</div>
            </div>
        </div>

        <div class="chat-container">
            <!-- 现代化头部 -->
            <div class="chat-header" id="chat-header">
                <div class="header-left">
                    <div class="header-info" id="header-conv-info">
                        <h1 id="header-conv-title" contenteditable="false">AI 运维助手</h1>
                    </div>
                </div>
                <div class="header-meta-row">
                    <div id="header-service-status" class="chat-status status-online">
                        <span class="status-dot"></span>
                        <span class="status-text" id="header-status-text">AI在线</span>
                    </div>
                    <span id="header-updated-time">—</span>
                </div>
            </div>

            <!-- 消息区域 -->
            <div class="chat-messages" id="chat-messages">
                <!-- 欢迎界面 -->
                <div class="welcome-message" id="welcome-screen">
                    <div class="welcome-icon">👋</div>
                    <div class="welcome-title">欢迎使用 AI 运维助手</div>
                    <div class="welcome-subtitle">专业解决 WMS 仓库管理系统问题</div>

                    <div class="welcome-features">
                        <div class="feature-item">
                            <div class="feature-icon">🔍</div>
                            <div class="feature-title">智能诊断</div>
                            <div class="feature-desc">快速识别并解决系统问题</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">⚡</div>
                            <div class="feature-title">即时响应</div>
                            <div class="feature-desc">7x24小时在线技术支持</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📚</div>
                            <div class="feature-title">专业知识库</div>
                            <div class="feature-desc">基于丰富的运维经验</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷问题按钮（常态隐藏，聚焦输入时用上方建议下拉替代） -->
            <div class="quick-actions">
                <div class="quick-actions-title">
                    💡 常见问题快速解决
                </div>
                <div class="quick-buttons">
                    <button class="quick-btn" data-question="二维码扫描不出来怎么办？">
                        🔍 二维码扫描问题

                    </button>
                    <button class="quick-btn" data-question="无法入库，系统提示找不到订单">
                        📦 入库订单问题
                    </button>
                    <button class="quick-btn" data-question="出库时提示库存冻结怎么处理？">
                        📤 出库库存问题
                    </button>
                    <button class="quick-btn" data-question="系统报错提示账期不对">
                        📅 账期错误问题
                    </button>
                    <button class="quick-btn" data-question="库存显示不适合移动">
                        🚫 库存移动限制
                    </button>
                    <button class="quick-btn" data-question="需要提前入库过账怎么操作？">
                        ⏰ 提前过账操作
                    </button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-area">
                <div class="input-container">
                    <!-- 增强输入框 - 三层结构 -->
                    <div class="enhanced-input-container">
                        <!-- 上部：常见问题按钮区域 -->
                        <div class="input-suggestions-area">
                            <button id="faq-open-btn" type="button" class="tool-btn faq-open-btn" title="常见问题">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                                    <path d="M9.5 9a2.5 2.5 0 0 1 5 0c0 1.5-1 2-2 2s-1.5 1-1.5 2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                    <circle cx="12" cy="17" r="1" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>

                        <!-- 中部：文字输入区域 -->
                        <div class="input-text-area">
                            <textarea
                                id="user-input"
                                class="input-field"
                                placeholder="发送消息给 AI助手"
                                rows="1"
                            ></textarea>

                        </div>

                        <!-- 下部：工具栏区域 -->
                        <div class="input-bottom-toolbar">
                            <div class="toolbar-left">
                                <button id="add-btn" type="button" class="tool-btn" title="添加附件">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                    </svg>
                                </button>
                                <button id="image-btn" type="button" class="tool-btn" title="上传图片">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                        <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                                        <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </button>
                                <button id="file-btn" type="button" class="tool-btn" title="上传文件">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </button>
                                <span class="toolbar-divider"></span>
                                <!-- AI模型选择器（移到左侧） -->
                                <select id="model-selector" class="model-selector" title="选择AI模型">
                                    <!-- 动态加载模型选项 -->
                                </select>
                                <div class="ai-priority-wrapper" title="开启：直接使用大模型回答（不走本地规则）; 关闭：先用本地分类再调用模型">
                                    <div id="ai-priority-toggle" class="ai-toggle on" role="switch" aria-checked="true" tabindex="0">
                                        <div class="ai-toggle-knob"></div>
                                    </div>
                                    <span id="ai-priority-label">AI优先</span>
                                </div>

                            </div>
                            <div class="toolbar-right">
                                <span class="kbd-hint">按 Enter 发送 • Shift+Enter 换行</span>
                                <!-- 发送按钮放在底部最右侧 -->
                                <button id="send-btn" class="send-btn" title="发送">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                        <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 常见问题选择弹窗（放在 body 底部，避免嵌套问题） -->
    <div id="faq-modal" class="faq-modal-overlay">
        <div class="faq-modal">
            <div class="faq-modal-header">
                <span>选择常见问题</span>
                <button id="faq-close" class="faq-close-btn" aria-label="关闭">✕</button>
            </div>
            <div class="faq-modal-list">
                <button type="button" class="faq-item" data-question="二维码扫描不出来怎么办？">🔍 二维码扫描不出来怎么办？</button>
                <button type="button" class="faq-item" data-question="无法入库，系统提示找不到订单">📦 无法入库，系统提示找不到订单</button>
                <button type="button" class="faq-item" data-question="出库时提示库存冻结怎么处理？">📤 出库时提示库存冻结怎么处理？</button>
                <button type="button" class="faq-item" data-question="系统报错提示账期不对">📅 系统报错提示账期不对</button>
                <button type="button" class="faq-item" data-question="库存显示不适合移动">🚫 库存显示不适合移动</button>
                <button type="button" class="faq-item" data-question="需要提前入库过账怎么操作？">⏰ 需要提前入库过账怎么操作？</button>


            </div>
        </div>
    </div>

    <script src="../../ai.js"></script>
    <script src="../../scripts/storage/sqlitePersistence.js"></script>
    <script src="../../scripts/ui/modal.js"></script>
    <script src="../../components/conversation-list/conversation.js"></script>
















        // AI 优先开关逻辑
        function updateAIPriorityUI() {
            if (aiPriority) {
                aiPriorityToggle.classList.add('on');
                aiPriorityToggle.classList.remove('off');
                aiPriorityToggle.setAttribute('aria-checked', 'true');
            } else {
                aiPriorityToggle.classList.remove('on');
                aiPriorityToggle.classList.add('off');
                aiPriorityToggle.setAttribute('aria-checked', 'false');
            }
            // 文案保持固定“AI优先”避免困扰
            aiPriorityLabel.textContent = 'AI优先';
            setServiceStatus('busy');
            setTimeout(() => { setServiceStatus('online'); }, 1200);
        }



        // ====== 会话管理 ====== //
    const conversationListEl = document.getElementById('conversation-list');
    const newChatBtn = document.getElementById('new-chat-btn') || newChatBtnTop;
    let sqliteDb = null; // sql.js Database instance

        let conversations = [];
        let currentConversation = null;
        // Bridge local variables with window to keep components and inline script in sync
        try {
            Object.defineProperty(window, 'conversations', {
                configurable: true,
                get(){ return conversations; },
                set(v){ conversations = Array.isArray(v) ? v : []; }
            });
            Object.defineProperty(window, 'currentConversation', {
                configurable: true,
                get(){ return currentConversation; },
                set(v){ currentConversation = v || null; }
            });
        } catch(e) {
            // Fallback in non-strict environments
            window.conversations = conversations;
            window.currentConversation = currentConversation;
        }

        const STORAGE_KEY = 'aiwms_conversations_v1';
        // 存储与渲染安全阈值，防止 localStorage 过大导致内存暴涨
        const STORAGE_MAX_BYTES = 1_500_000; // 约 1.5MB
        const MAX_CONVS = 30;
        const MAX_MSGS_PER_CONV = 200;
        const MAX_MSGS_RENDER = 200;
        const MAX_CONTENT_LEN = 4000;

        function sanitizeConversations(arr){
            try{
                return (Array.isArray(arr)?arr:[])
                    .slice(0, MAX_CONVS)
                    .map(c => ({
                        ...c,
                        createdAt: c.createdAt ? Number(c.createdAt) : c.createdAt,
                        updatedAt: c.updatedAt ? Number(c.updatedAt) : c.updatedAt,
                        messages: (c.messages||[])
                            .slice(-MAX_MSGS_PER_CONV)
                            .map(m => ({
                                ...m,
                                ts: m.ts ? Number(m.ts) : m.ts,
                                content: typeof m.content === 'string' ? m.content.slice(0, MAX_CONTENT_LEN) : ''
                            }))
                    }));
            }catch{ return []; }
        }





        // convSearchKeyword 已由 components/conversation-list/conversation.js 定义

        // 
        //    renderConversationList  components/conversation-list/conversation.js
        // 
        // 点击页面任意空白处，关闭已打开的菜单
        //     
        //     components/conversation-list/conversation.js 

        // switchConversation 已迁移到 components/conversation-list/conversation.js

        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => {
                createConversation();
            });
        }

        // 初始加载
        loadConversations();
        if (!conversations.length) {
            createConversation();
        } else {
            currentConversation = conversations[0];
            renderConversationList();
            switchConversation(currentConversation.id);
        }
        // 初始化 SQLite（异步，不阻塞渲染）
        setTimeout(()=>{ initSQLite(); }, 0);


        function refreshHeaderInfo(resetTitle=false) {
            if (!currentConversation) return;
            if (resetTitle) {
                headerConvTitle.textContent = '新对话';
            } else {
                headerConvTitle.textContent = currentConversation.title || 'AI 运维助手';
            }
            const t = currentConversation.updatedAt || currentConversation.createdAt;
            headerUpdatedTime.textContent = new Date(t).toLocaleString('zh-CN',{hour:'2-digit',minute:'2-digit'});
        }

        //     
        //     components/conversation-list/conversation.js 


    <!-- 统一 UI 弹窗容器 -->
    <div id="ui-modal-overlay" class="ui-modal-overlay" role="dialog" aria-modal="true" aria-hidden="true">
        <div class="ui-modal" role="document">
            <div class="ui-modal-header">
                <span id="ui-modal-title">提示</span>
                <button id="ui-modal-close" class="ui-modal-close" aria-label="关闭">✕</button>
            </div>
            <div class="ui-modal-body">
                <p id="ui-modal-message" class="ui-modal-message"></p>
                <textarea id="ui-modal-input" class="ui-modal-input hidden" placeholder="请输入"></textarea>
            </div>
            <div class="ui-modal-footer">
                <button id="ui-modal-cancel" class="btn btn-secondary">取消</button>
                <button id="ui-modal-ok" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
    <script src="../../components/header/header.js"></script>
    <script src="./help.js"></script>

</body>
</html>