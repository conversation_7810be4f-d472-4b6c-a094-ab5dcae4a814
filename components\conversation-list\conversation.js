// Conversation list (sidebar) logic extracted from help.html
(function(){
  'use strict';
  // Ensure globals exist
  if (!('convSearchKeyword' in window)) window.convSearchKeyword = '';

  // Helpers to safe-closest inside shadowed event targets
  function safeClosest(node, sel){
    const el = node instanceof Element ? node : node && node.parentElement;
    return el ? el.closest(sel) : null;
  }

  // createConversation
  window.createConversation = function createConversation(force){
    force = !!force;
    const userInput = document.getElementById('user-input');
    if (!force && window.currentConversation && window.currentConversation.messages.length === 0 && !window.currentConversation.titleEdited) {
      userInput && userInput.focus();
      return window.currentConversation;
    }
    const conv = {
      id: Date.now() + '-' + Math.random().toString(36).slice(2,6),
      title: '新对话',
      titleEdited: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      messages: []
    };
    window.conversations.unshift(conv);
    window.currentConversation = conv;
    try{
      if(window.sqliteDb){
        window.sqliteDb.run('INSERT INTO conversations(id,title,createdAt,updatedAt) VALUES(?,?,?,?) ON CONFLICT(id) DO UPDATE SET title=excluded.title, updatedAt=excluded.updatedAt',[conv.id, conv.title, conv.createdAt, conv.updatedAt]);
      }
    }catch(e){ console.warn('[AI][sqlite] 新建会话写入失败', e); }
    if (typeof window.saveConversations==='function') window.saveConversations();
    if (typeof window.scheduleSQLiteSaveSafely==='function') window.scheduleSQLiteSaveSafely();
    if (typeof window.renderConversationList==='function') ; // this function (self)
    window.renderConversationList();
    if (typeof window.clearChatUI==='function') window.clearChatUI();
    if (typeof window.refreshHeaderInfo==='function') window.refreshHeaderInfo(true);
    if (typeof window.showWelcomeIfEmpty==='function') window.showWelcomeIfEmpty();
    return conv;
  };

  // renderConversationList
  window.renderConversationList = function renderConversationList(){
    const conversationListEl = document.getElementById('conversation-list');
    if (!conversationListEl) return;
    conversationListEl.innerHTML = '';
    const conversations = window.conversations || [];
    if (!conversations.length) {
      conversationListEl.innerHTML = '<div class="empty-conv-hint">暂无对话，点击上方新建</div>';
      return;
    }
    const list = window.convSearchKeyword ? conversations.filter(c => (c.title||'').toLowerCase().includes(window.convSearchKeyword)) : conversations;
    if (!list.length) {
      conversationListEl.innerHTML = '<div class="empty-conv-hint">无匹配结果</div>';
      return;
    }
    const fmt = (ts)=>{
      const d = new Date(ts);
      const now = new Date();
      const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
      const startOfYesterday = startOfToday - 86400000;
      const full = d.toLocaleString('zh-CN', { year:'numeric', month:'2-digit', day:'2-digit', hour:'2-digit', minute:'2-digit' });
      if (ts >= startOfToday) return { text: d.toLocaleTimeString('zh-CN',{hour:'2-digit',minute:'2-digit'}), tip: full };
      if (ts >= startOfYesterday) return { text: '昨天 ' + d.toLocaleTimeString('zh-CN',{hour:'2-digit',minute:'2-digit'}), tip: full };
      const day = d.getDay();
      const daysFromNow = Math.floor((startOfToday - new Date(d.getFullYear(), d.getMonth(), d.getDate()).getTime())/86400000);
      if (daysFromNow < 7) {
        const weekMap = ['周日','周一','周二','周三','周四','周五','周六'];
        return { text: weekMap[day] + ' ' + d.toLocaleTimeString('zh-CN',{hour:'2-digit',minute:'2-digit'}), tip: full };
      }
      if (now.getFullYear() === d.getFullYear()) return { text: d.toLocaleString('zh-CN',{month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit'}), tip: full };
      return { text: d.toLocaleString('zh-CN',{year:'numeric',month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit'}), tip: full };
    };
    list.forEach(conv => {
      const item = document.createElement('div');
      item.className = 'conversation-item' + (conv === window.currentConversation ? ' active':'');
      item.dataset.id = conv.id;
      const title = document.createElement('div');
      title.className = 'conversation-item-title';
      title.textContent = conv.title || '未命名对话';
      const meta = document.createElement('div');
      meta.className = 'conversation-item-meta';
      const ts = Number(conv.updatedAt || conv.createdAt || Date.now());
      const f = fmt(ts);
      const label = (()=>{
        const d = new Date(ts);
        const now = new Date();
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
        const startOfYesterday = startOfToday - 86400000;
        if (ts >= startOfToday) return '今天';
        if (ts >= startOfYesterday) return '昨天';
        const day = d.getDay();
        const daysFromNow = Math.floor((startOfToday - new Date(d.getFullYear(), d.getMonth(), d.getDate()).getTime())/86400000);
        if (daysFromNow < 7) return ['周日','周一','周二','周三','周四','周五','周六'][day];
        if (now.getFullYear() === d.getFullYear()) return d.toLocaleDateString('zh-CN',{month:'2-digit',day:'2-digit'});
        return d.toLocaleDateString('zh-CN',{year:'numeric',month:'2-digit',day:'2-digit'});
      })();
      const star = conv.starred ? '⭐ ' : '';
      meta.innerHTML = `<span class="meta-left" title="${f.tip}">${star}${label}</span><span class="meta-right" title="${f.tip}">${f.text}</span>`;
      item.appendChild(title);
      item.appendChild(meta);
      const trigger = document.createElement('div');
      trigger.className = 'conv-actions-trigger';
      trigger.title = '更多操作';
      trigger.innerHTML = '⋯';
      item.appendChild(trigger);
      const menu = document.createElement('div');
      menu.className = 'conv-actions-menu';
      menu.innerHTML = `
        <div class="conv-actions-item" data-act="rename">重命名</div>
        <div class="conv-actions-item" data-act="favorite">${conv.starred ? '取消收藏' : '收藏'}</div>
        <div class="conv-actions-sep"></div>
        <div class="conv-actions-item danger" data-act="delete">删除</div>
      `;
      item.appendChild(menu);
      item.addEventListener('click', (e) => {
        if (safeClosest(e.target, '.conv-actions-trigger') || safeClosest(e.target, '.conv-actions-menu')) return;
        window.switchConversation(conv.id);
      });
      trigger.addEventListener('click', (e) => {
        e.stopPropagation();
        document.querySelectorAll('.conv-actions-menu.open').forEach(m => { if (m!==menu) m.classList.remove('open'); m.style.display='none'; });
        menu.style.display = menu.classList.contains('open') ? 'none' : 'block';
        menu.classList.toggle('open');
      });
      menu.addEventListener('click', async (e) => {
        const actEl = safeClosest(e.target, '.conv-actions-item');
        if (!actEl) return;
        const act = actEl.dataset.act;
        if (act === 'rename') {
          const openModal = window.openModal;
          const nv = openModal ? await openModal({ title: '重命名对话', message: '', mode: 'prompt', placeholder: '输入新的对话标题', okText: '保存', defaultValue: conv.title || '' }) : null;
          if (nv && nv.trim()) window.renameConversation(conv, nv.trim());
        } else if (act === 'favorite') {
          conv.starred = !conv.starred;
          if (typeof window.saveConversations==='function') window.saveConversations();
          window.renderConversationList();
        } else if (act === 'delete') {
          const openModal = window.openModal;
          const ok = openModal ? await openModal({ title: '删除对话', message: '确认删除该对话？此操作不可撤回。', mode: 'confirm', okText: '删除', okType: 'danger' }) : true;
          if (ok) window.deleteConversation(conv);
        }
        menu.classList.remove('open');
        menu.style.display='none';
      });
      conversationListEl.appendChild(item);
    });
  };

  // switchConversation
  window.switchConversation = function switchConversation(id){
    const conversations = window.conversations || [];
    const conv = conversations.find(c => c.id === id);
    if (!conv) return;
    window.currentConversation = conv;
    if (typeof window.refreshHeaderInfo==='function') window.refreshHeaderInfo();
    window.renderConversationList();
    if (typeof window.clearChatUI==='function') window.clearChatUI();
    if (!conv.messages.length) {
      if (typeof window.showWelcomeIfEmpty==='function') window.showWelcomeIfEmpty();
      return;
    }
    const MAX = window.MAX_MSGS_RENDER || 200;
    (conv.messages||[]).slice(-MAX).forEach(m => {
      if (typeof window.addMessage==='function') window.addMessage(m.content, m.role === 'user' ? 'user' : 'ai', false, false);
    });
  };

  // renameConversation
  window.renameConversation = function renameConversation(conv, newTitle){
    if(!conv) return;
    const t = (newTitle||'').trim();
    if(!t) return;
    conv.title = t;
    conv.titleEdited = true;
    conv.updatedAt = Date.now();
    try{ if(window.sqliteDb) window.sqliteDb.run('INSERT INTO conversations(id,title,createdAt,updatedAt) VALUES(?,?,?,?) ON CONFLICT(id) DO UPDATE SET title=excluded.title, updatedAt=excluded.updatedAt',[conv.id, conv.title, conv.createdAt||Date.now(), conv.updatedAt]); if (typeof window.scheduleSQLiteSaveSafely==='function') window.scheduleSQLiteSaveSafely(); }catch(e){ console.warn('[AI][sqlite] 重命名写入失败', e); }
    if (typeof window.saveConversations==='function') window.saveConversations();
    if (typeof window.scheduleSQLiteSaveSafely==='function') window.scheduleSQLiteSaveSafely();
    window.renderConversationList();
    if (typeof window.refreshHeaderInfo==='function') window.refreshHeaderInfo();
  };

  // deleteConversation
  window.deleteConversation = function deleteConversation(conv){
    if(!conv) return;
    const conversations = window.conversations || [];
    const idx = conversations.findIndex(c=>c.id===conv.id);
    if(idx===-1) return;
    conversations.splice(idx,1);
    if(window.currentConversation && window.currentConversation.id===conv.id){
      window.currentConversation = conversations[0] || null;
      if (typeof window.clearChatUI==='function') window.clearChatUI();
      if(window.currentConversation){
        const MAX = window.MAX_MSGS_RENDER || 200;
        (window.currentConversation.messages||[]).slice(-MAX).forEach(m=>{
          if (typeof window.addMessage==='function') window.addMessage(m.content, m.role==='user'?'user':'ai', false, false);
        });
      }
    }
    try{ if(window.sqliteDb){ window.sqliteDb.run('DELETE FROM messages WHERE conv_id=?',[conv.id]); window.sqliteDb.run('DELETE FROM conversations WHERE id=?',[conv.id]); if (typeof window.scheduleSQLiteSaveSafely==='function') window.scheduleSQLiteSaveSafely(); } }catch(e){ console.warn('[AI][sqlite] 删除会话失败', e); }
    if (typeof window.saveConversations==='function') window.saveConversations();
    if (typeof window.scheduleSQLiteSaveSafely==='function') window.scheduleSQLiteSaveSafely();
    window.renderConversationList();
    if (typeof window.refreshHeaderInfo==='function') window.refreshHeaderInfo();
  };

  // One-time registrations (search input & global click/esc handlers)
  function oneTimeBind(){
    if (window.__conv_list_inited) return; window.__conv_list_inited = true;
    const searchInput = document.getElementById('conversation-search');
    if (searchInput){
      searchInput.addEventListener('input', (e)=>{
        window.convSearchKeyword = (e.target.value||'').trim().toLowerCase();
        window.renderConversationList();
      });
    }
    document.addEventListener('click', (e) => {
      if (safeClosest(e.target, '.conv-actions-menu') || safeClosest(e.target, '.conv-actions-trigger')) return;
      document.querySelectorAll('.conv-actions-menu.open').forEach(m => { m.classList.remove('open'); m.style.display='none'; });
    });
    document.addEventListener('keydown',(e)=>{
      if(e.key==='Escape'){
        document.querySelectorAll('.conv-actions-menu.open').forEach(m=>{ m.classList.remove('open'); m.style.display='none'; });
      }
    });
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', oneTimeBind);
  } else {
    oneTimeBind();
  }
})();

