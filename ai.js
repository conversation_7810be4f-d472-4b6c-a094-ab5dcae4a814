(function (global) {
  'use strict';

  // Lightweight, browser-friendly AI service for Ark/OpenAI-compatible APIs.
  // Exposes: AI.configure, AI.getConfig, AI.chatCompletion, AI.visionDescribe, AI.mockParse, AI.loadConfig
  // Usage (direct):
  //   AI.configure({ baseURL: 'https://ark.cn-beijing.volces.com/api/v3', apiKey: 'YOUR_ARK_KEY', model: 'your-model-id' });
  //   const res = await AI.chatCompletion({ messages: [{ role: 'user', content: '你好' } ] });
  // Warning: Putting api<PERSON>ey in frontend is insecure; prefer proxy mode in production.

  const AI = (function () {
    const DEFAULTS = {
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
      apiKey: null,
      model: null, // e.g. 'ep-...'
      timeout: 30000,
      headers: {},
      mode: 'direct', // 'direct' | 'mock' | 'proxy' (proxy endpoint not implemented here)
    };

    let config = { ...DEFAULTS };

    function configure(opts) {
      if (!opts || typeof opts !== 'object') return;
      config = { ...config, ...opts };
    }

    function getConfig() {
      // Mask apiKey in getters to avoid accidental leaks in logs
      const { apiKey, ...rest } = config;
      return { ...rest, apiKey: apiKey ? '***' : null };
    }

    async function loadConfig(input) {
      // input can be URL string (JSON) or plain object
      try {
        if (!input) throw new Error('loadConfig: missing input');
        if (typeof input === 'string') {
          // Fetch JSON config; note: fetch may be blocked for file:// in some browsers
          const resp = await fetch(input, { cache: 'no-cache' });
          if (!resp.ok) throw new Error(`loadConfig HTTP ${resp.status}`);
          const data = await resp.json();
          configure(validateConfig(data));
          return getConfig();
        }
        if (typeof input === 'object') {
          configure(validateConfig(input));
          return getConfig();
        }
        throw new Error('loadConfig: invalid input type');
      } catch (err) {
        console.warn('[AI.loadConfig] Failed:', err);
        throw err;
      }
    }

    function validateConfig(obj) {
      const out = { ...obj };
      if (out.baseURL && typeof out.baseURL !== 'string') throw new Error('baseURL must be string');
      if (out.apiKey && typeof out.apiKey !== 'string') throw new Error('apiKey must be string');
      if (out.model && typeof out.model !== 'string') throw new Error('model must be string');
      if (out.timeout && typeof out.timeout !== 'number') throw new Error('timeout must be number');
      if (out.headers && typeof out.headers !== 'object') throw new Error('headers must be object');
      if (out.mode && !['direct','mock','proxy'].includes(out.mode)) throw new Error('mode must be direct/mock/proxy');
      return out;
    }

    function normalizeMessages(messages) {
      // Accepts OpenAI-style messages, where content can be a string or an array of parts
      if (!Array.isArray(messages)) return [];
      return messages.map((m) => {
        if (typeof m?.content === 'string') return m;
        // If content is array of parts, keep as-is (Ark OpenAI-compatible supports this for vision)
        return { role: m.role, content: m.content };
      });
    }

    function extractLastUserText(messages) {
      if (!Array.isArray(messages)) return '';
      for (let i = messages.length - 1; i >= 0; i--) {
        if (messages[i].role === 'user') {
          const c = messages[i].content;
          if (typeof c === 'string') return c;
          if (Array.isArray(c)) {
            const textPart = c.find((p) => p?.type === 'text');
            if (textPart?.text) return textPart.text;
          }
        }
      }
      return '';
    }

    async function chatCompletion({ messages, model, stream = false, temperature, top_p, max_tokens } = {}) {
      if (config.mode === 'mock') {
        // Return a quick mock that echoes last user text
        const text = extractLastUserText(messages) || '（无输入）';
        return {
          id: 'mock-' + Date.now(),
          mock: true,
          created: Math.floor(Date.now() / 1000),
          choices: [
            {
              index: 0,
              message: { role: 'assistant', content: `模拟回复：${text}` },
              finish_reason: 'stop',
            },
          ],
        };
      }

      if (config.mode === 'proxy') {
        throw new Error('AI.proxy 模式未实现：请在后端提供 /api/ai/chat 代理并在 AI.chatCompletion 中添加调用逻辑。');
      }

      if (!config.apiKey) {
        throw new Error('AI.configure: apiKey 为空（直连模式）。建议在生产中使用代理模式避免在前端暴露密钥。');
      }

      const url = `${config.baseURL.replace(/\/+$/, '')}/chat/completions`;
      const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config.apiKey}`,
        ...config.headers,
      };

      const body = {
        model: model || config.model,
        messages: normalizeMessages(messages),
        stream: !!stream,
        temperature,
        top_p,
        max_tokens,
      };
      Object.keys(body).forEach((k) => body[k] === undefined && delete body[k]);

      const controller = new AbortController();
      const timer = setTimeout(() => controller.abort(), config.timeout || 30000);
      try {
        const resp = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(body),
          signal: controller.signal,
        });
        if (!resp.ok) {
          const text = await resp.text().catch(() => '');
          throw new Error(`AI.chatCompletion HTTP ${resp.status}: ${text.slice(0, 500)}`);
        }
        if (stream) {
          const reader = resp.body && resp.body.getReader ? resp.body.getReader() : null;
          return { stream: true, reader };
        }
        return await resp.json();
      } finally {
        clearTimeout(timer);
      }
    }

    async function visionDescribe({ imageUrl, question = '请描述图片内容', model } = {}) {
      const messages = [
        {
          role: 'user',
          content: [
            { type: 'image_url', image_url: { url: imageUrl } },
            { type: 'text', text: question },
          ],
        },
      ];
      return chatCompletion({ messages, model });
    }

    // Reusable lightweight mock intent parser (same as the demo's logic, but returned as data only)
    function mockParse(text) {
      const lower = String(text || '').toLowerCase();
      let intent = 'query';
      let confidence = 0.7;
      let payload = {};
      if (/出库|拣货|发货/.test(lower)) {
        intent = 'create_outbound';
        confidence = 0.93;
        const qtyMatch = String(text).match(/(\d+)\s*(件|pcs|ea)?/i);
        const qty = qtyMatch ? parseInt(qtyMatch[1]) : 1;
        const custMatch = String(text).match(/客户([A-Za-z0-9\-]*)/i);
        const cust = custMatch ? '客户' + custMatch[1] : '默认客户';
        payload = { sku: 'A123', qty, customer: cust, priority: '近效期优先' };
      } else if (/入库|收货|接收/.test(lower)) {
        intent = 'create_inbound';
        confidence = 0.92;
        const supplierMatch = String(text).match(/供货方([A-Za-z0-9\-]*)/i);
        const supplier = supplierMatch ? supplierMatch[1] : '未知供应商';
        payload = { supplier, items: [{ sku: 'A123', qty: /(\d+)托盘/.test(String(text)) ? 300 : 100 }], target: '配件仓-收货区' };
      } else if (/退货/.test(lower)) {
        intent = 'create_return';
        confidence = 0.88;
        payload = { sku: 'A123', qty: 10, reason: '客户退货' };
      } else if (/模板|流程/.test(lower)) {
        intent = 'create_flow';
        confidence = 0.86;
        payload = { name: '新流程', steps: ['step1', 'step2'] };
      } else {
        intent = 'query';
        confidence = 0.6;
        payload = { query: text };
      }
      return { intent, confidence, payload };
    }

    return { configure, getConfig, chatCompletion, visionDescribe, mockParse, loadConfig };
  })();

  if (typeof module !== 'undefined' && module.exports) {
    module.exports = AI;
  } else {
    global.AI = AI;
  }
})(typeof window !== 'undefined' ? window : globalThis);
