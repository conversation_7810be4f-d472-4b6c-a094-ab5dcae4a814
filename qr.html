<!doctype html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>设备二维码（5个）</title>
		<link rel="stylesheet" href="styles/base.css" />
		<style>
			:root {
				--qr-size: 180px;
				--gap: 16px;
				--fg: #111;
				--muted: #666;
			}
			body {
				margin: 0 auto;
				padding: 24px;
				max-width: 1080px;
				font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Noto Sans", "Microsoft YaHei", sans-serif;
				color: var(--fg);
				background: #fff;
			}
			header {
				margin-bottom: 16px;
				display: flex;
				align-items: baseline;
				justify-content: space-between;
				gap: 12px;
			}
			header h1 {
				font-size: 20px;
				margin: 0;
			}
			header .note { color: var(--muted); font-size: 12px; }

			.grid {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
				gap: var(--gap);
			}
			.card {
				border: 1px solid #e5e7eb;
				border-radius: 10px;
				padding: 16px;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background: #fafafa;
			}
			.qr-box {
				width: var(--qr-size);
				height: var(--qr-size);
				display: grid;
				place-items: center;
				background: #fff;
				border-radius: 8px;
				box-shadow: 0 1px 2px rgba(0,0,0,0.06);
				margin-bottom: 10px;
				overflow: hidden;
			}
			.label {
				font-weight: 600;
				letter-spacing: 0.2px;
			}
			.sub {
				margin-top: 2px;
				font-size: 12px;
				color: var(--muted);
			}
			@media print {
				body { padding: 0; max-width: none; }
				header { display: none; }
				.card { page-break-inside: avoid; border: none; background: #fff; }
				.grid { gap: 12px; }
			}
		</style>
	</head>
	<body>
		<header>
			<h1>设备二维码（5个）</h1>
			<div class="note">可直接扫码测试 · 打印友好</div>
		</header>

		<main class="grid" id="qr-grid" aria-live="polite"></main>

			<script>
				// 可根据需要修改或扩展设备列表
				const devices = [
					{ id: 'DEV-0001', name: '设备 1' },
					{ id: 'DEV-0002', name: '设备 2' },
					{ id: 'DEV-0003', name: '设备 3' },
					{ id: 'DEV-0004', name: '设备 4' },
					{ id: 'DEV-0005', name: '设备 5' },
				];

				// 编码内容：为通用扫描测试，这里使用纯文本设备ID
				// 如需跳转 URL，可改为：`https://your.domain/device/${id}` 或自定义协议
				function payloadFor(id) {
					return `DEVICE:${id}`; // 简洁直观，便于调试
				}

				const grid = document.getElementById('qr-grid');

				function createCard(dev) {
					const card = document.createElement('section');
					card.className = 'card';
					const box = document.createElement('div');
					box.className = 'qr-box';
					box.setAttribute('aria-label', `设备 ${dev.id} 的二维码`);
					const label = document.createElement('div');
					label.className = 'label';
					label.textContent = dev.name || dev.id;
					const sub = document.createElement('div');
					sub.className = 'sub';
					sub.textContent = dev.id;
					card.appendChild(box);
					card.appendChild(label);
					card.appendChild(sub);
					grid.appendChild(card);
					return { card, box };
				}

				function renderWithLib() {
					devices.forEach((dev) => {
						const { box } = createCard(dev);
						new QRCode(box, {
							text: payloadFor(dev.id),
							width: parseInt(getComputedStyle(box).width, 10),
							height: parseInt(getComputedStyle(box).height, 10),
							colorDark: '#000000',
							colorLight: '#ffffff',
							correctLevel: window.QRCode?.CorrectLevel?.M ?? 1,
						});
					});
				}

				// 最终兜底：使用在线图片服务生成二维码（无需 JS 库）
				function renderAsImages() {
					devices.forEach((dev) => {
						const { box } = createCard(dev);
						const size = Math.min(
							parseInt(getComputedStyle(box).width, 10) || 180,
							parseInt(getComputedStyle(box).height, 10) || 180
						);
						const img = document.createElement('img');
						img.alt = `设备 ${dev.id} 的二维码`;
						img.width = size;
						img.height = size;
						const data = encodeURIComponent(payloadFor(dev.id));
						img.src = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${data}`;
						box.appendChild(img);
					});
				}

				// 加载库（多 CDN 顺序兜底），加载成功后再渲染
				function loadQRCodeLibAndRender() {
					const urls = [
						'https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js',
						'https://unpkg.com/qrcodejs@1.0.0/qrcode.min.js'
					];

					function tryNext() {
						if (!urls.length) {
							renderAsImages();
							return;
						}
						const url = urls.shift();
						const s = document.createElement('script');
						s.src = url;
						s.async = true;
						s.onload = () => {
							if (window.QRCode) {
								renderWithLib();
							} else {
								tryNext();
							}
						};
						s.onerror = () => tryNext();
						document.head.appendChild(s);
					}
					tryNext();
				}

				// 启动
				loadQRCodeLibAndRender();
			</script>
	</body>
	</html>
