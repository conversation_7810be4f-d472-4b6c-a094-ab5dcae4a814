/* Conversation list (sidebar) styles extracted from help.html */
.sidebar { display: flex; flex-direction: column; width: 230px; background: linear-gradient(180deg, #ffffff 0%, #F9FAFB 100%); color:#1f2937; padding:12px 12px 10px; gap:10px; height:100%; box-sizing:border-box; border-right:1px solid #E5E7EB; transition: width .28s ease; overflow: hidden; }
.sidebar-top { position: sticky; top: 0; z-index: 5; }
.sidebar-mid { overflow: auto; padding-top: 6px; }
.sidebar-top { background: rgba(255,255,255,0.95); border-bottom: 1px solid #E5E7EB; box-shadow: 0 1px 0 rgba(0,0,0,0.04); border-radius: 8px 8px 0 0; padding: 6px; }
.icon-btn-mini { width:40px; height:40px; background:rgba(0,0,0,0.04); border:1px solid #E5E7EB; border-radius:10px; display:flex; align-items:center; justify-content:center; cursor:pointer; color:#4B5563; font-size:16px; transition:.2s background; }
.icon-btn-mini:hover { background:rgba(0,0,0,0.06); }
.new-chat-btn--top { height:32px; padding: 0 12px; border-radius:8px; }
.sidebar-search { position:relative; }
.sidebar-search input { width:100%; background:#F9FAFB; border:1px solid #E5E7EB; padding:7px 28px 7px 10px; border-radius:8px; font-size:12px; color:#374151; outline:none; }
.sidebar-search input::placeholder { color:#9CA3AF; }
.sidebar-search svg { position:absolute; right:8px; top:50%; transform:translateY(-50%); width:14px; height:14px; color:#9CA3AF; }
.sidebar-header { display:flex; align-items:center; justify-content:space-between; padding:6px 4px 4px; }
.new-chat-btn { width:100%; background:linear-gradient(135deg,var(--primary) 0%, var(--primary-dark) 100%); border:none; border-radius:10px; color:#fff; padding:10px 12px; font-size:12px; cursor:pointer; font-weight:600; display:flex; align-items:center; gap:6px; letter-spacing:.5px; }
.new-chat-btn:hover { filter:brightness(1.05); }
.conversation-list { flex:1; overflow:auto; display:flex; flex-direction:column; gap:6px; margin-top:6px; padding-right:4px; }
.conversation-item { background:#ffffff; border:1px solid #E5E7EB; border-radius:10px; padding:10px 10px 8px; font-size:12px; cursor:pointer; display:flex; flex-direction:column; gap:4px; position:relative; transition:.25s background, .25s border, .25s transform; }
.conversation-item:hover { background:#F3F4F6; }
.conversation-item.active { background:#F3FDE7; border-color: rgba(165,205,57,0.6); }
.conversation-item.active::before { content:""; position:absolute; left:-12px; top:8px; bottom:8px; width:4px; border-radius:0 4px 4px 0; background:linear-gradient(180deg,var(--primary) 0%, var(--primary-dark) 100%); }
.conversation-item:hover .conv-actions-trigger { opacity:1; }
.conv-actions-trigger { position:absolute; top:6px; right:6px; width:18px; height:18px; display:flex; align-items:center; justify-content:center; font-size:13px; color:#9CA3AF; cursor:pointer; opacity:0; transition:.2s opacity, .2s color; }
.conv-actions-trigger:hover { color:#4B5563; }
/* Dropdown menu inside conversation item */
.conv-actions-menu { position:absolute; top:26px; right:6px; z-index:2000; background:#ffffff; color:#374151; border:1px solid #E5E7EB; border-radius:8px; width:auto; min-width:100px; padding:4px 0; box-shadow:0 8px 20px -4px rgba(0,0,0,0.15); font-size:12px; display:none; }
.conv-actions-menu.open { display:block; animation:fadeScale .12s ease; }
.conv-actions-item { padding:6px 12px; cursor:pointer; white-space:nowrap; line-height:1.3; }
.conv-actions-item:hover { background:#F3F4F6; }
.conv-actions-item.danger { color:#ef4444; font-weight:500; }
.conv-actions-sep { height:1px; margin:3px 0; background:#E5E7EB; }
.conversation-item .starred-icon { color:#fbbf24; margin-right:6px; }
@keyframes fadeScale { from { opacity:0; transform:scale(.95); } to { opacity:1; transform:scale(1); } }
.conversation-item-title { font-weight:600; color:#1F2937; line-height:1.3; font-size:12px; }
.conversation-item-meta { font-size:10px; color:#6B7280; display:flex; align-items:center; }
.conversation-item-meta .meta-left { color:#6B7280; }
.conversation-item.active .conversation-item-meta .meta-left { color: var(--primary-dark); }
.conversation-item-meta .meta-right { margin-left:auto; color:#6B7280; }
.empty-conv-hint { font-size:11px; color:#6B7280; text-align:center; padding:12px 4px; }

/* Responsive */
@media (max-width: 768px) {
  .sidebar { display: none; }
}

