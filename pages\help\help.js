// pages/help/help.js
// 页面级初始化与装配（从 help.html 迁移，确保零视觉差与零交互变化）
(function(){
  'use strict';

  document.addEventListener('DOMContentLoaded', () => {
    // 页面加载渐显
    setTimeout(() => { document.body.classList.add('loaded'); }, 100);

    // 悬停微动效（保持与之前一致）
    const quickBtns = document.querySelectorAll('.quick-btn');
    quickBtns.forEach(btn => {
      btn.addEventListener('mouseenter', () => {
        btn.style.transform = 'translateY(-2px) scale(1.02)';
      });
      btn.addEventListener('mouseleave', () => {
        btn.style.transform = 'translateY(0) scale(1)';
      });
    });

    // 快捷问题按钮点击：填充输入框并发送
    const quickButtons = document.querySelectorAll('.quick-btn');
    const userInput = document.getElementById('user-input');
    quickButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const question = btn.dataset.question || '';
        if (userInput) userInput.value = question;
        if (typeof window.handleSend === 'function') window.handleSend();
      });
    });

    // 常见问题弹窗交互
    const faqOpenBtn = document.getElementById('faq-open-btn');
    const faqModal = document.getElementById('faq-modal');
    const faqClose = document.getElementById('faq-close');

    if (faqOpenBtn) {
      faqOpenBtn.addEventListener('click', () => {
        if (faqModal) faqModal.style.display = 'flex';
      });
    }
    if (faqClose) {
      faqClose.addEventListener('click', () => {
        if (faqModal) faqModal.style.display = 'none';
      });
    }
    if (faqModal) {
      faqModal.addEventListener('click', (e) => {
        if (e.target === faqModal) faqModal.style.display = 'none';
      });
    }
    document.querySelectorAll('.faq-item').forEach(item => {
      item.addEventListener('click', () => {
        const q = item.dataset.question || '';
        if (q && userInput) userInput.value = q;
        if (faqModal) faqModal.style.display = 'none';
        if (typeof window.handleSend === 'function') window.handleSend();
      });
    });
  });
})();



// ===== Inline JS migrated from help.html (part 1) =====
(function(){
  'use strict';
  // DOM refs
  const chatMessages = document.getElementById('chat-messages');
  const userInput = document.getElementById('user-input');
  const sendBtn = document.getElementById('send-btn');
  const statusText = document.getElementById('header-status-text');
  const welcomeScreen = document.getElementById('welcome-screen');
  const modelSelector = document.getElementById('model-selector');
  const faqOpenBtn = document.getElementById('faq-open-btn');
  const faqModal = document.getElementById('faq-modal');
  const faqClose = document.getElementById('faq-close');
  const newChatBtnTop = document.getElementById('new-chat-btn-top');
  const headerConvTitle = document.getElementById('header-conv-title');
  const headerConvSub = document.getElementById('header-conv-sub');
  const headerUpdatedTime = document.getElementById('header-updated-time');
  const aiPriorityToggle = document.getElementById('ai-priority-toggle');
  const aiPriorityLabel = document.getElementById('ai-priority-label');
  let aiPriority = true;

  // Toast
  function showToast(message, type='success', duration=1600){
    try{
      const container = document.getElementById('toast-container');
      if(!container) return;
      const el = document.createElement('div');
      el.className = `toast ${type}`;
      const icon = type==='success' ? '✅' : (type==='error' ? '⚠️' : 'ℹ️');
      el.innerHTML = `<span class="icon">${icon}</span><span>${message}</span>`;
      container.appendChild(el);
      const t = setTimeout(()=>{
        el.style.transition = 'opacity .2s ease, transform .2s ease';
        el.style.opacity = '0';
        el.style.transform = 'translateY(4px)';
        setTimeout(()=>{ if (el.parentNode) el.parentNode.removeChild(el); }, 200);
      }, Math.max(800, duration));
      el.addEventListener('click', ()=>{ clearTimeout(t); if (el.parentNode) el.parentNode.removeChild(el); });
    }catch{}
  }
  window.showToast = showToast;

  // System prompt
  const SYSTEM_PROMPT = `你是一个专业的 WMS (仓库管理系统) 运维助手。你的主要职责是帮助用户解决仓库操作中的各种问题。
\n请根据以下知识库回答用户问题：\n\n## 二维码相关问题\n1. **车身无码**: 在系统中手动输入车身铭牌上的序列号\n2. **二维码与铭牌不一致**: 以铭牌序列号为准手动输入，后续在OA系统提交"电子码补码修正"申请\n3. **需要提前入库过账**: 联系上游供应商，在"直接交货确认"环节走"港口交货"流程\n\n## 入库问题\n1. **查询不到入库订单**: 检查是否属于"港口交货"，如果是则在LTC系统完成收货\n2. **过账异常，提示非账期月**: 联系公司财务部门处理\n\n## 出库问题\n1. **S4状态为"冻结"**: 先完成该物料的"收货入库"动作，或检查并解除冻结状态\n2. **提示库存短缺**: 同上，检查库存状态和入库情况\n3. **账期错误**: 修改出库凭证的OB日期，使其与当前账期匹配\n4. **库存不适合移动**:\n   - 入库环节报错：联系 MM (物料管理) 模块顾问\n   - 出库环节报错：联系 SD (销售与分销) 模块顾问\n\n请用简洁、专业的语言回答，提供具体的操作步骤。如果问题超出知识库范围，建议联系相关技术支持。`;

  // Local FAQ classification
  const FAQ_KB = [
    { key:'qrcode_issue', title:'二维码扫描/车身无码', keywords:['二维码','扫码','扫描不','扫不出','车身无码','没码','识别不了','扫描不了','扫描失败'],
      answer:`🔍 <strong>二维码问题解决方案</strong>\n\n<strong>🚗 车身无码情况</strong>\n• 在系统中手动输入车身铭牌上的序列号完成操作\n\n<strong>⚠️ 二维码与铭牌不一致</strong>\n• 以铭牌序列号为准手动录入\n• 之后到 OA 系统提交 “电子码补码修正” 申请\n\n<strong>💡 提示</strong>\n若仍无法处理，请补充：终端设备 / 具体界面 / 报错截图。` },
    { key:'inbound_order_missing', title:'查询不到入库订单', keywords:['入库','找不到订单','没有订单','查不到订单','无对应入库订单','不存在入库订单','入库单没','入库单找不到','收货单','入库过账','非账期月'],
      answer:`📦 <strong>入库订单问题解决方案</strong>\n\n<strong>1️⃣ 查询不到入库订单</strong>\n• 核实是否为 “港口交货” 模式；若是，请在 LTC 系统进行收货\n\n<strong>2️⃣ 过账异常/提示非账期月</strong>\n• 需要联系公司财务部门处理账期\n\n请继续说明：是否港口交货 / 当前界面 / 错误提示。` },
    { key:'outbound_frozen', title:'出库冻结/库存短缺', keywords:['出库','冻结','库存短缺','库存不够','库存不足','库存冻结','ob日期','账期','不适合移动'],
      answer:`📤 <strong>出库相关问题解决方案</strong>\n\n<strong>❄️ 库存冻结/短缺</strong>\n• 先完成该物料的 “收货入库”\n• 或检查并解除冻结状态\n\n<strong>📅 账期错误 / OB 日期</strong>\n• 修改出库凭证 OB 日期，使其匹配当前账期\n\n<strong>🚫 库存不适合移动</strong>\n• 入库阶段报错：联系 MM 模块顾问\n• 出库阶段报错：联系 SD 模块顾问\n\n请补充：影响的物料号 / 当前单据号 / 系统截图。` },
    { key:'advance_posting', title:'需要提前入库过账', keywords:['提前过账','提前入库','先过账','先入库','预先过账'],
      answer:`⏰ <strong>提前入库过账指引</strong>\n\n• 联系上游供应商，在 “直接交货确认” 环节选择 “港口交货” 流程\n• 确认主数据与交货单据已生效\n• 若为紧急计划需求，说明业务场景并走加急审批流程\n\n补充：请提供物料/批次/计划单号以便进一步核查。` },
  ];
  function classifyQuestion(text){
    const original = text || '';
    const normalized = original.toLowerCase().replace(/\s+/g,'');
    let best = null;
    const hasScan = /(扫码|扫描|二维码)/.test(normalized);
    const hasInboundMissing = /(找不到订单|没有订单|查不到订单|没有对应的入库订单|无对应入库订单|不存在入库订单|入库单没|入库单找不到)/.test(normalized);
    FAQ_KB.forEach(item=>{
      let hit=0; item.keywords.forEach(kw=>{ if (normalized.includes(kw.toLowerCase().replace(/\s+/g,''))) hit += kw.length; });
      if(hit>0){
        let score = hit/(normalized.length+4);
        if (hasScan && hasInboundMissing) {
          if (item.key==='inbound_order_missing') score+=0.5; else if (item.key==='qrcode_issue') score-=0.3;
        }
        if (!best || score>best.score) best={ matched:true, key:item.key, title:item.title, answer:item.answer, score };
      }
    });
    if (best && hasScan && hasInboundMissing && best.key==='qrcode_issue'){
      const inbound = FAQ_KB.find(i=>i.key==='inbound_order_missing');
      if (inbound) best={ matched:true, key:inbound.key, title:inbound.title+'（含扫码场景判别）', answer:'⚠️ 识别到您描述了扫码行为，同时系统提示 “没有对应的入库订单”。扫码多为触发动作，核心问题是入库单缺失。\n\n'+inbound.answer, score:(best.score||0)+0.4 };
    }
    return best || { matched:false, key:null, title:null, answer:null, score:0 };
  }

  // Add message
  function addMessage(content, type='ai', isTyping=false, persist=true){
    try{ if (welcomeScreen) welcomeScreen.style.display='none'; }catch{}
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    const avatar = document.createElement('div'); avatar.className='message-avatar'; avatar.textContent = type==='ai'?'🤖':'👤';
    const contentDiv = document.createElement('div'); contentDiv.className='message-content';
    if (isTyping){
      contentDiv.innerHTML = '<div class="typing-indicator"><div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div></div>';
    } else {
      contentDiv.innerHTML = (content||'').replace(/\n/g,'<br>');
      const footer = document.createElement('div'); footer.className='message-footer';
      const timeDiv = document.createElement('div'); timeDiv.className='message-time'; timeDiv.textContent = new Date().toLocaleTimeString('zh-CN',{hour:'2-digit',minute:'2-digit'});
      footer.appendChild(timeDiv);
      const tools = document.createElement('div'); tools.className='message-tools';
      if (type==='ai'){
        const copyBtn = document.createElement('button');
        copyBtn.className='copy-btn'; copyBtn.type='button'; copyBtn.title='复制内容'; copyBtn.textContent='⧉';
        copyBtn.addEventListener('click', async (e)=>{
          e.stopPropagation();
          const tmp = document.createElement('div'); tmp.innerHTML = contentDiv.innerHTML;
          tmp.querySelectorAll('.message-footer,.message-time,.message-tools,.copy-btn,.recognized-tag').forEach(el=>el.remove());
          const text = tmp.innerText.replace(/\u00A0/g,' ').trim();
          try{
            if (navigator.clipboard && window.isSecureContext) await navigator.clipboard.writeText(text);
            else { const ta=document.createElement('textarea'); ta.value=text; ta.style.position='fixed'; ta.style.top='-9999px'; document.body.appendChild(ta); ta.focus(); ta.select(); document.execCommand('copy'); document.body.removeChild(ta); }
            copyBtn.classList.add('copied'); copyBtn.title='已复制'; setTimeout(()=>{ copyBtn.classList.remove('copied'); copyBtn.title='复制内容'; }, 1200);
            showToast('已复制到剪贴板','success');
          }catch(err){ copyBtn.title='复制失败'; setTimeout(()=>{ copyBtn.title='复制内容'; },1500); showToast('复制失败，请重试','error'); }
        });
        tools.appendChild(copyBtn);
      }
      footer.appendChild(tools);
      contentDiv.appendChild(footer);
    }
    messageDiv.appendChild(avatar); messageDiv.appendChild(contentDiv); chatMessages && chatMessages.appendChild(messageDiv);

    if (!isTyping && persist && window.currentConversation){
      window.currentConversation.messages.push({ id: Date.now()+'-'+Math.random().toString(36).slice(2,8), role: type==='user'?'user':'assistant', content, ts: Date.now() });
      if (!window.currentConversation.titleEdited){ const plain=(content||'').replace(/<[^>]+>/g,'').trim(); if (plain){ window.currentConversation.title = plain.slice(0,20) || window.currentConversation.title; } }
      window.currentConversation.updatedAt = Date.now();
      try{ if (window.sqliteDb){ const last = window.currentConversation.messages[window.currentConversation.messages.length-1]; if(last){ window.sqliteDb.run('INSERT OR REPLACE INTO messages(id,conv_id,role,content,ts) VALUES(?,?,?,?,?)',[last.id, window.currentConversation.id, last.role, last.content, last.ts]); window.sqliteDb.run('INSERT INTO conversations(id,title,createdAt,updatedAt) VALUES(?,?,?,?) ON CONFLICT(id) DO UPDATE SET title=excluded.title, updatedAt=excluded.updatedAt', [window.currentConversation.id, window.currentConversation.title||'未命名对话', window.currentConversation.createdAt||Date.now(), window.currentConversation.updatedAt]); } } }catch(e){ console.warn('[AI][sqlite] 写入消息失败', e); }
      if (typeof window.saveConversations==='function') window.saveConversations();
      if (typeof window.renderConversationList==='function') window.renderConversationList();
      if (typeof window.refreshHeaderInfo==='function') window.refreshHeaderInfo();
    }

    setTimeout(()=>{ if(chatMessages) chatMessages.scrollTo({ top: chatMessages.scrollHeight, behavior:'smooth' }); }, 100);
    return messageDiv;
  }
  window.addMessage = addMessage;

  async function sendToAI(userMessage){
    try{
      setServiceStatus('busy');
      const selectedModel = modelSelector && modelSelector.value; if (selectedModel) AI.configure({ model: selectedModel });
      const response = await AI.chatCompletion({ messages:[ {role:'system', content: SYSTEM_PROMPT}, { role:'user', content: userMessage } ], temperature: 0.3 });
      setServiceStatus('online');
      if (response.choices && response.choices[0] && response.choices[0].message) return response.choices[0].message.content;
      throw new Error('AI响应格式异常');
    }catch(error){ setServiceStatus('error'); console.error('AI调用失败:', error); return getFallbackResponse(userMessage); }
  }

  function getFallbackResponse(message){
    const msg = (message||'').toLowerCase();
    if (msg.includes('二维码') || msg.includes('扫码') || msg.includes('无码')) return `🔍 <strong>二维码问题解决方案</strong>\n\n<strong>🚗 车身无码情况</strong>\n• 在系统中手动输入车身铭牌上的序列号完成操作\n\n<strong>⚠️ 二维码与铭牌不一致</strong>\n• 当前操作以铭牌序列号为准，手动输入完成\n• 后续在OA系统提交"电子码补码修正"申请\n\n<strong>💡 温馨提示</strong>\n如需更详细的操作指导，请告诉我具体遇到的情况。`;
    if (msg.includes('入库') && (msg.includes('订单') || msg.includes('找不到'))) return `📦 <strong>入库订单问题解决方案</strong>\n\n<strong>1️⃣ 查询不到入库订单</strong>\n• 首先检查此订单是否属于"港口交货"\n• 如果是，则无需在此处收货，请在LTC系统完成收货\n\n<strong>2️⃣ 过账异常，提示非账期月</strong>\n• 请联系公司财务部门进行处理\n\n<strong>🤔 请告诉我</strong>\n您遇到的是哪种具体情况？我可以提供更精准的解决方案。`;
    if (msg.includes('出库') && (msg.includes('冻结') || msg.includes('库存'))) return `📤 <strong>出库相关问题解决方案</strong>\n\n<strong>❄️ 库存冻结/短缺</strong>\n• 请仓管员先在系统中完成该物料的"收货入库"动作\n• 或检查并解除冻结状态\n\n<strong>📅 账期错误</strong>\n• 修改出库凭证的OB日期，使其与当前账期匹配\n\n<strong>🚫 库存不适合移动</strong>\n• 入库环节报错：联系 MM (物料管理) 模块顾问\n• 出库环节报错：联系 SD (销售与分销) 模块顾问\n\n<strong>💬 需要帮助？</strong>\n请告诉我需要详细说明哪个步骤的操作流程。`;
    return `😊 <strong>很抱歉，我需要更多信息来帮助您</strong>\n\n<strong>🔄 建议您可以：</strong>\n1. 重新描述问题，包含更多关键词\n2. 使用上方的快捷问题按钮\n3. 联系技术支持获得专业帮助\n\n<strong>💡 或者直接告诉我：</strong>\n• 您在操作哪个功能模块？\n• 具体的错误提示是什么？\n• 问题出现的操作步骤？\n\n我会根据您的描述提供更精准的解决方案！`;
  }

  async function handleSend(){
    const message = (userInput && userInput.value || '').trim();
    if (!message) return;
    addMessage(message, 'user');
    if (userInput) userInput.value = '';
    if (sendBtn) sendBtn.disabled = true;
    if (!aiPriority){
      const cls = classifyQuestion(message); const CONF_THRESHOLD = 0.18;
      if (cls.matched && cls.score >= CONF_THRESHOLD){
        const typingMsgLocal = addMessage('', 'ai', true);
        setTimeout(()=>{ if (chatMessages && typingMsgLocal && typingMsgLocal.parentNode===chatMessages) chatMessages.removeChild(typingMsgLocal); addMessage(`<div class=\"recognized-tag\">已识别标准问题</div><strong>${cls.title}</strong>\n\n${cls.answer}`,'ai'); if (sendBtn) sendBtn.disabled=false; userInput && userInput.focus(); }, 300);
        return;
      }
    }
    const typingMsg = addMessage('', 'ai', true);
    const aiResponse = await sendToAI(message);
    if (chatMessages && typingMsg && typingMsg.parentNode===chatMessages) chatMessages.removeChild(typingMsg);
    addMessage(aiResponse, 'ai');
    if (sendBtn) sendBtn.disabled = false; userInput && userInput.focus();
  }
  window.handleSend = handleSend;

  // Listeners
  if (sendBtn) sendBtn.addEventListener('click', handleSend);
  if (userInput) userInput.addEventListener('keypress', (e)=>{ if (e.key==='Enter' && !e.shiftKey){ e.preventDefault(); handleSend(); } });
  if (userInput) userInput.addEventListener('input', ()=>{ userInput.style.height='auto'; userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px'; });
})();


// ===== Inline JS migrated from help.html (part 2: AI config, model selector, status) =====
(function(){
  'use strict';
  const statusText = document.getElementById('header-status-text');
  const modelSelector = document.getElementById('model-selector');
  const aiPriorityToggle = document.getElementById('ai-priority-toggle');
  const aiPriorityLabel = document.getElementById('ai-priority-label');
  let aiPriority = true;

  function populateModelsFromConfig(){
    if (!modelSelector) return;
    modelSelector.innerHTML='';
    const cfg = AI.getConfig();
    const models = Array.isArray(cfg.models)&&cfg.models.length ? cfg.models : (cfg.model ? [cfg.model] : []);
    if (models.length===0){ const opt=document.createElement('option'); opt.value=''; opt.textContent='（未配置模型）'; modelSelector.appendChild(opt); return; }
    models.forEach((modelId, index)=>{
      const opt=document.createElement('option'); opt.value=modelId; let displayName=modelId;
      if (modelId.includes('doubao')){ if (modelId.includes('flash')) displayName='豆包-Flash'; else if (modelId.includes('pro')) displayName='豆包-Pro'; else if (modelId.includes('32k')) displayName='豆包-32K'; else displayName='豆包'; }
      else if (modelId.includes('gpt')) displayName=modelId.toUpperCase();
      else displayName = modelId.length>20 ? modelId.substring(0,17)+'...' : modelId;
      if (index===0) displayName += ' (默认)'; opt.textContent = displayName; modelSelector.appendChild(opt);
    });
    if (models.length>0) modelSelector.value = models[0];
  }
  window.populateModelsFromConfig = populateModelsFromConfig;

  (async()=>{
    try{
      const isFileProto = location.protocol==='file:';
      if (isFileProto){
        await AI.loadConfig({ mode:'direct', baseURL:'https://ark.cn-beijing.volces.com/api/v3', apiKey:'f1510fc9-df05-4cc7-b21f-85f7249800b6', model:'doubao-seed-1-6-flash-250715', models:['doubao-seed-1-6-flash-250715','doubao-seed-1-6-pro-250715','doubao-seed-1-6-32k-250715'], timeout:30000, headers:{} });
        console.log('[AI] file:// 模式使用内置配置'); populateModelsFromConfig();
      } else {
        await AI.loadConfig('../../ai-config.json'); console.log('[AI] 运维助手配置加载成功'); populateModelsFromConfig();
      }
    }catch(e){
      console.warn('[AI] 从 ai-config.json 加载失败:', e);
      try{ await AI.loadConfig({ mode:'direct', baseURL:'https://ark.cn-beijing.volces.com/api/v3', apiKey:'f1510fc9-df05-4cc7-b21f-85f7249800b6', model:'doubao-seed-1-6-flash-250715', timeout:30000, headers:{} }); console.log('[AI] 使用内置配置'); populateModelsFromConfig(); }
      catch(fallbackErr){ console.error('[AI] 兜底配置也失败:', fallbackErr); if (modelSelector){ const opt=document.createElement('option'); opt.value=''; opt.textContent='配置加载失败'; modelSelector.appendChild(opt); } }
    }
  })();

  function setServiceStatus(state){
    const container = document.getElementById('header-service-status');
    if(!container || !statusText) return;
    container.classList.remove('status-online','status-busy','status-error','status-offline');
    switch(state){
      case 'online': container.classList.add('status-online'); statusText.textContent='AI在线'; break;
      case 'busy': container.classList.add('status-busy'); statusText.textContent='AI处理中'; break;
      case 'error': container.classList.add('status-error'); statusText.textContent='AI离线'; break;
      case 'offline': default: container.classList.add('status-offline'); statusText.textContent='AI离线'; break;
    }
  }
  window.setServiceStatus = setServiceStatus;

  if (modelSelector){
    modelSelector.addEventListener('change', (e)=>{
      const selectedModel = e.target.value; if (!selectedModel) return;
      console.log(`[AI] 切换模型到: ${selectedModel}`);
      const selectedOption = e.target.options[e.target.selectedIndex];
      const displayName = selectedOption.textContent.replace(' (默认)','');
      statusText && (statusText.textContent = `模型: ${displayName}`);
      setTimeout(()=>{ statusText && (statusText.textContent='在线服务'); }, 2000);
    });
  }

  function updateAIPriorityUI(){
    if (!aiPriorityToggle || !aiPriorityLabel) return;
    if (aiPriority){ aiPriorityToggle.classList.add('on'); aiPriorityToggle.classList.remove('off'); aiPriorityToggle.setAttribute('aria-checked','true'); }
    else { aiPriorityToggle.classList.remove('on'); aiPriorityToggle.classList.add('off'); aiPriorityToggle.setAttribute('aria-checked','false'); }
    aiPriorityLabel.textContent='AI优先'; setServiceStatus('busy'); setTimeout(()=>{ setServiceStatus('online'); }, 1200);
  }
  function toggleAIPriority(){ aiPriority = !aiPriority; updateAIPriorityUI(); }
  if (aiPriorityToggle){
    aiPriorityToggle.addEventListener('click', toggleAIPriority);
    aiPriorityToggle.addEventListener('keydown',(e)=>{ if (e.key==='Enter' || e.key===' '){ e.preventDefault(); toggleAIPriority(); } });
  }
  updateAIPriorityUI();

  let lastHealthOk = false;
  async function healthCheck(){
    try{
      const res = await AI.chatCompletion({ messages:[{ role:'user', content:'ping' }], temperature:0, max_tokens:5 });
      if (res && res.choices){ if (!lastHealthOk) setServiceStatus('online'); lastHealthOk = true; } else { throw new Error('unexpected health schema'); }
    }catch(err){ setServiceStatus('error'); lastHealthOk = false; }
  }
  setTimeout(()=>{ healthCheck(); setInterval(healthCheck, 30000); }, 2500);
})();


// ===== Inline JS migrated from help.html (part 3: conversations, storage, sqlite, header) =====
(function(){
  'use strict';
  const chatMessages = document.getElementById('chat-messages');
  const newChatBtnTop = document.getElementById('new-chat-btn-top');
  const newChatBtn = document.getElementById('new-chat-btn') || newChatBtnTop;
  const headerConvTitle = document.getElementById('header-conv-title');
  const headerConvSub = document.getElementById('header-conv-sub');
  const headerUpdatedTime = document.getElementById('header-updated-time');
  const welcomeScreen = document.getElementById('welcome-screen');

  let sqliteDb = null; window.sqliteDb = null;
  let conversations = []; let currentConversation = null;
  try{
    Object.defineProperty(window,'conversations',{ configurable:true, get(){ return conversations; }, set(v){ conversations = Array.isArray(v)?v:[]; } });
    Object.defineProperty(window,'currentConversation',{ configurable:true, get(){ return currentConversation; }, set(v){ currentConversation = v||null; } });
  }catch(e){ window.conversations = conversations; window.currentConversation = currentConversation; }

  const STORAGE_KEY = 'aiwms_conversations_v1';
  const STORAGE_MAX_BYTES = 1_500_000; // ~1.5MB
  const MAX_CONVS = 30;
  const MAX_MSGS_PER_CONV = 200;
  const MAX_MSGS_RENDER = 200; window.MAX_MSGS_RENDER = MAX_MSGS_RENDER;
  const MAX_CONTENT_LEN = 4000;

  function sanitizeConversations(arr){
    try{
      return (Array.isArray(arr)?arr:[]).slice(0, MAX_CONVS).map(c=>({ ...c, createdAt: c.createdAt?Number(c.createdAt):c.createdAt, updatedAt: c.updatedAt?Number(c.updatedAt):c.updatedAt, messages: (c.messages||[]).slice(-MAX_MSGS_PER_CONV).map(m=>({ ...m, ts: m.ts?Number(m.ts):m.ts, content: typeof m.content==='string' ? m.content.slice(0, MAX_CONTENT_LEN) : '' })) }));
    }catch{ return []; }
  }
  function loadConversations(){
    try{ const raw = localStorage.getItem(STORAGE_KEY); if(!raw){ conversations=[]; return; } if (raw.length>STORAGE_MAX_BYTES){ console.warn('[AI] 本地会话数据过大，已自动清理'); localStorage.removeItem(STORAGE_KEY); conversations=[]; return; } const data = JSON.parse(raw); conversations = sanitizeConversations(data); }
    catch(e){ console.warn('[AI] 读取本地会话失败，已重置:', e); conversations=[]; try{ localStorage.removeItem(STORAGE_KEY); }catch{} }
  }
  function saveConversations(){
    try{ const clean = sanitizeConversations(conversations); let s = JSON.stringify(clean); if (s.length>STORAGE_MAX_BYTES) s = JSON.stringify(clean.slice(0,10)); localStorage.setItem(STORAGE_KEY, s); }
    catch(e){ console.warn('[AI] 保存本地会话失败:', e); }
  }
  window.saveConversations = saveConversations;
  window.scheduleSQLiteSaveSafely = function(){ try{ window.SQLitePersistence && window.SQLitePersistence.scheduleSave(); }catch{} };

  async function initSQLite(){
    try{
      if(typeof initSqlJs!=='function'){ console.warn('[AI] sql.js 未加载'); return; }
      const SQL = await initSqlJs({ locateFile: f => 'https://cdn.jsdelivr.net/npm/sql.js@1.10.2/dist/' + f });
      const existedBytes = await window.SQLitePersistence.loadBytes();
      sqliteDb = existedBytes ? new SQL.Database(existedBytes) : new SQL.Database(); window.sqliteDb = sqliteDb;
      sqliteDb.run(`
        PRAGMA journal_mode=MEMORY;
        CREATE TABLE IF NOT EXISTS conversations ( id TEXT PRIMARY KEY, title TEXT, createdAt INTEGER, updatedAt INTEGER );
        CREATE TABLE IF NOT EXISTS messages ( id TEXT PRIMARY KEY, conv_id TEXT, role TEXT, content TEXT, ts INTEGER );
      `);
      (conversations||[]).slice(0, MAX_CONVS).forEach(conv=>{
        try{ sqliteDb.run('INSERT INTO conversations(id,title,createdAt,updatedAt) VALUES(?,?,?,?) ON CONFLICT(id) DO UPDATE SET title=excluded.title, updatedAt=excluded.updatedAt',[conv.id, conv.title||'未命名对话', conv.createdAt||Date.now(), conv.updatedAt||Date.now()]);
             (conv.messages||[]).slice(-MAX_MSGS_PER_CONV).forEach(m=>{ try{ sqliteDb.run('INSERT OR REPLACE INTO messages(id,conv_id,role,content,ts) VALUES(?,?,?,?,?)',[m.id, conv.id, m.role, m.content, m.ts||Date.now()]); }catch(e){ console.warn('[AI][sqlite] 写入消息失败', e); } });
        }catch(e){ console.warn('[AI][sqlite] 写入会话失败', e); }
      });
      console.log('[AI] SQLite 初始化完成');
      try{
        if ((!conversations || conversations.length===0)){
          const rs = sqliteDb.exec('SELECT id,title,createdAt,updatedAt FROM conversations ORDER BY updatedAt DESC');
          if (rs && rs[0] && rs[0].values && rs[0].values.length){
            const cols = rs[0].columns; const items = rs[0].values.map(v=>Object.fromEntries(cols.map((c,i)=>[c,v[i]])));
            const msgStmt = sqliteDb.prepare('SELECT id,role,content,ts FROM messages WHERE conv_id=$id ORDER BY ts ASC');
            const newConvs = [];
            for(const c of items.slice(0, MAX_CONVS)){
              msgStmt.bind({ $id: c.id }); const msgs=[]; while(msgStmt.step()){ const row = msgStmt.getAsObject(); msgs.push({ id: row.id, role: row.role, content: (row.content||'').toString().slice(0, MAX_CONTENT_LEN), ts: row.ts }); if (msgs.length>MAX_MSGS_PER_CONV) msgs.shift(); }
              msgStmt.reset(); newConvs.push({ id:c.id, title:c.title||'未命名对话', titleEdited:true, createdAt:c.createdAt||Date.now(), updatedAt:c.updatedAt||Date.now(), messages:msgs });
            }
            msgStmt.free(); conversations = sanitizeConversations(newConvs); saveConversations(); window.renderConversationList && window.renderConversationList(); if (conversations.length){ currentConversation = conversations[0]; window.switchConversation && window.switchConversation(currentConversation.id); }
          }
        }
      }catch(e){ console.warn('[AI][sqlite] 回灌失败', e); }
      window.SQLitePersistence.scheduleSave();
      window.addEventListener('beforeunload', ()=>{ try{ window.SQLitePersistence.flushNow(); }catch{} });
    }catch(e){ console.warn('[AI] SQLite 初始化失败:', e); }
  }

  function clearChatUI(){ if (chatMessages) chatMessages.innerHTML=''; }
  function showWelcomeIfEmpty(){ if (currentConversation && currentConversation.messages.length===0 && welcomeScreen){ welcomeScreen.style.display='block'; } }
  window.clearChatUI = clearChatUI; window.showWelcomeIfEmpty = showWelcomeIfEmpty;

  function refreshHeaderInfo(resetTitle=false){
    if (!currentConversation) return;
    if (resetTitle) headerConvTitle && (headerConvTitle.textContent='新对话'); else headerConvTitle && (headerConvTitle.textContent = currentConversation.title || 'AI 运维助手');
    const t = currentConversation.updatedAt || currentConversation.createdAt; headerUpdatedTime && (headerUpdatedTime.textContent = new Date(t).toLocaleString('zh-CN',{hour:'2-digit',minute:'2-digit'})); headerConvSub && (headerConvSub.textContent='WMS 仓库管理系统智能客服');
  }
  window.refreshHeaderInfo = refreshHeaderInfo;

  if (newChatBtn){ newChatBtn.addEventListener('click', ()=>{ window.createConversation && window.createConversation(); }); }
  const miniNewBtn = document.getElementById('mini-new'); if (miniNewBtn){ miniNewBtn.addEventListener('click', ()=> window.createConversation && window.createConversation()); }
  const miniSettings = document.getElementById('mini-settings'); if (miniSettings){ miniSettings.addEventListener('click', ()=> alert('设置面板开发中')); }

  // Initial load
  loadConversations();
  if (!conversations.length){ window.createConversation && window.createConversation(); }
  else { currentConversation = conversations[0]; window.renderConversationList && window.renderConversationList(); window.switchConversation && window.switchConversation(currentConversation.id); }
  setTimeout(()=>{ initSQLite(); }, 0);
})();
