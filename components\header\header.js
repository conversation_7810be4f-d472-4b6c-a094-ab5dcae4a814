// Header title inline rename behavior (double-click to edit, Enter save, <PERSON>s<PERSON> cancel)
(function(){
  if (typeof document === 'undefined') return;
  const headerConvTitle = document.getElementById('header-conv-title');
  if(!headerConvTitle) return;
  function startHeaderTitleEdit(){
    if(!(window.currentConversation)) return;
    headerConvTitle.setAttribute('contenteditable','true');
    headerConvTitle.classList.add('editing');
    const range = document.createRange();
    range.selectNodeContents(headerConvTitle);
    const sel = window.getSelection();
    sel.removeAllRanges(); sel.addRange(range);
    headerConvTitle.focus();
  }
  function finishHeaderTitleEdit(commit){
    headerConvTitle.removeAttribute('contenteditable');
    headerConvTitle.classList.remove('editing');
    const txt = (headerConvTitle.textContent||'').trim();
    if(commit && txt){
      // Use existing global renameConversation if available
      if (typeof window.renameConversation === 'function' && window.currentConversation){
        window.renameConversation(window.currentConversation, txt);
      } else {
        // Fallback: dispatch a custom event for the page orchestrator to handle
        document.dispatchEvent(new CustomEvent('aiwms:header-rename', { detail: { title: txt } }));
      }
    } else {
      const cv = window.currentConversation;
      headerConvTitle.textContent = (cv && cv.title) ? cv.title : 'AI 运维助手';
    }
  }
  headerConvTitle.addEventListener('dblclick', (e)=>{ e.stopPropagation(); startHeaderTitleEdit(); });
  headerConvTitle.addEventListener('keydown', (e)=>{
    if(e.key==='Enter'){ e.preventDefault(); finishHeaderTitleEdit(true); }
    else if(e.key==='Escape'){ e.preventDefault(); finishHeaderTitleEdit(false); }
  });
  headerConvTitle.addEventListener('blur', ()=>{ if(headerConvTitle.isContentEditable) finishHeaderTitleEdit(true); });
})();

