(function(){
  const P = {
    dbName: 'aiwms-idb', store: 'sqlite', key: 'convdb', throttleMs: 1500, _timer: null
  };
  function openIDB(){ return new Promise((resolve, reject)=>{ const req = indexedDB.open(P.dbName, 1); req.onupgradeneeded = ()=>{ const db = req.result; if(!db.objectStoreNames.contains(P.store)) db.createObjectStore(P.store); }; req.onerror=()=>reject(req.error); req.onsuccess=()=>resolve(req.result); }); }
  async function saveBytes(bytes){ try { const db = await openIDB(); const tx = db.transaction(P.store,'readwrite'); tx.objectStore(P.store).put(bytes, P.key); await new Promise((res,rej)=>{ tx.oncomplete=()=>res(); tx.onerror=()=>rej(tx.error); }); db.close(); } catch(e){ console.warn('[AI][sqlite] 保存失败', e); } }
  async function loadBytes(){ try { const db = await openIDB(); const tx = db.transaction(P.store,'readonly'); const req = tx.objectStore(P.store).get(P.key); const bytes = await new Promise((res,rej)=>{ req.onsuccess=()=>res(req.result||null); req.onerror=()=>rej(req.error); }); db.close(); return bytes; } catch(e){ console.warn('[AI][sqlite] 载入失败', e); return null; } }
  function scheduleSave(){ if(!window.sqliteDb) return; clearTimeout(P._timer); P._timer = setTimeout(()=>{ try{ const bytes = window.sqliteDb.export(); saveBytes(bytes); }catch(e){ console.warn('[AI][sqlite] 导出失败', e); } }, P.throttleMs); }
  function flushNow(){ if(!window.sqliteDb) return; clearTimeout(P._timer); try{ const bytes = window.sqliteDb.export(); return saveBytes(bytes); }catch(e){ console.warn('[AI][sqlite] 导出失败', e); } }
  window.SQLitePersistence = { saveBytes, loadBytes, scheduleSave, flushNow };
  window.scheduleSQLiteSaveSafely = function(){ try { window.SQLitePersistence?.scheduleSave?.(); } catch {} };
})();
