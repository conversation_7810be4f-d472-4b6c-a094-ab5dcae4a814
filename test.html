<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>AI 能力测试台</title>
  <link id="theme-css" rel="stylesheet" href="./styles/theme-light.css" />
  <link rel="stylesheet" href="./styles/base.css" />
  <style>
    body { padding: 16px; }
    .row { display: flex; gap: 12px; align-items: center; flex-wrap: wrap; }
    .col { display: flex; flex-direction: column; gap: 6px; }
    .card { background: var(--card); padding: 12px; border-radius: 10px; box-shadow: 0 2px 8px var(--shadow-strong); }
    .label { font-size: 12px; color: var(--muted); }
    textarea, input, select { background: transparent; color: inherit; border:1px solid var(--border-soft); border-radius: 8px; padding: 8px; }
    textarea { width: 100%; min-height: 90px; }
    pre { background: var(--code-bg); color: var(--code-fg); padding: 10px; border-radius: 8px; overflow: auto; }
    .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 12px; }
    .btn { background: var(--accent); border: none; padding: 8px 12px; border-radius: 8px; color: var(--btn-foreground); cursor: pointer; font-weight: 600; }
    .secondary { background: var(--btn-secondary-bg); color: var(--btn-secondary-fg); }
  </style>
</head>
<body>
  <h1>AI 能力测试台</h1>
  <p class="label">用于快速验证 Ark（豆包）直连/Mock、多模态、以及对话。建议结合 <code>ai-config.json</code> 使用。</p>
  <div class="row" style="justify-content: flex-end; margin: 8px 0;">
    <button id="theme-toggle" class="btn secondary">切换主题</button>
  </div>

  <script src="./ai.js"></script>

  <section class="grid">
    <div class="card">
      <div class="col">
        <div class="label">模型选择（页面启动时自动读取 ai-config.json）</div>
        <div class="row" style="align-items: baseline; gap: 12px; margin-top: 6px;">
          <div class="label">当前模型</div>
          <select id="model-select"></select>
        </div>
        <div class="row" id="cfg-actions" style="display:none">
          <input id="pick-config" type="file" accept="application/json" style="display:none" />
          <button id="local-load" class="btn secondary">从本地选择 ai-config.json</button>
        </div>
        <pre id="config-view" style="margin-top:8px"></pre>
      </div>
    </div>

    <div class="card">
      <div class="col">
        <div class="label">文本对话（Chat Completion）</div>
        <textarea id="chat-input" placeholder="输入你的问题... 例：帮我写一个拣货 SOP 要点"></textarea>
        <div class="row">
          <button id="chat-send" class="btn">发送</button>
        </div>
        <pre id="chat-output"></pre>
      </div>
    </div>

    <div class="card">
      <div class="col">
        <div class="label">多模态（图像 + 文本）</div>
        <input id="vision-url" placeholder="图片 URL（公网可访问）" />
        <input id="vision-q" placeholder="提问（例如：这是哪里？）" />
        <div class="row">
          <button id="vision-send" class="btn">分析图片</button>
        </div>
        <pre id="vision-output"></pre>
      </div>
    </div>

    <div class="card">
      <div class="col">
        <div class="label">轻量意图解析（MockParse）</div>
        <input id="mock-text" placeholder="例如：出库给客户A 500件，优先近效期" />
        <button id="mock-run" class="btn">解析</button>
        <pre id="mock-output"></pre>
      </div>
    </div>
  </section>

  <script>
    // 主题切换（与 demo 保持一致）
    const themeLink = document.getElementById('theme-css');
    const themeToggleBtn = document.getElementById('theme-toggle');
    function setTheme(mode){
      themeLink.href = `./styles/theme-${mode}.css`;
      try { localStorage.setItem('theme', mode); } catch {}
    }
    const savedTheme = (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) || 'light';
    setTheme(savedTheme);
    themeToggleBtn?.addEventListener('click', ()=>{
      const current = themeLink.getAttribute('href').includes('theme-dark.css') ? 'dark' : 'light';
      setTheme(current === 'light' ? 'dark' : 'light');
    });

  const cfgView = document.getElementById('config-view');
  const cfgActions = document.getElementById('cfg-actions');
  const modelSelect = document.getElementById('model-select');

    function showConfig() {
      cfgView.textContent = JSON.stringify(AI.getConfig(), null, 2);
    }

    function populateModelsFromConfig() {
      // 支持 model: string 或 models: string[]
      modelSelect.innerHTML = '';
      const cfg = AI.getConfig();
      const models = Array.isArray(cfg.models) && cfg.models.length ? cfg.models : (cfg.model ? [cfg.model] : []);
      if (models.length === 0) {
        const opt = document.createElement('option');
        opt.value = '';
        opt.textContent = '（未配置模型）';
        modelSelect.appendChild(opt);
      } else {
        models.forEach((m, i) => {
          const opt = document.createElement('option');
          opt.value = m;
          opt.textContent = m + (i === 0 ? '（默认）' : '');
          modelSelect.appendChild(opt);
        });
      }
    }

    // 启动时自动加载配置；失败则使用内置默认配置
    (async () => {
      try {
        await AI.loadConfig('./ai-config.json');
        populateModelsFromConfig();
        showConfig();
      } catch (e) {
        console.warn('AI config auto-load failed:', e);
        // 使用内置默认配置作为兜底
        try {
          await AI.loadConfig({
            mode: 'direct',
            baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
            apiKey: 'f1510fc9-df05-4cc7-b21f-85f7249800b6',
            model: 'doubao-seed-1-6-flash-250715',
            timeout: 30000,
            headers: {}
          });
          populateModelsFromConfig();
          showConfig();
          cfgView.textContent = '使用内置默认配置（从 ai-config.json 读取失败）\n' + cfgView.textContent;
        } catch (fallbackErr) {
          cfgView.textContent = '配置加载失败：' + (e?.message || e) + '\n兜底配置也失败：' + (fallbackErr?.message || fallbackErr) + '\n你可以从本地选择 ai-config.json';
          cfgActions.style.display = 'flex';
        }
      }
    })();

    // 本地文件选择兜底（避免 file:// 环境下 fetch 失败）
    const pickInput = document.getElementById('pick-config');
    const localLoadBtn = document.getElementById('local-load');
    localLoadBtn.onclick = () => pickInput.click();
    pickInput.onchange = () => {
      const file = pickInput.files && pickInput.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = async () => {
        try {
          const json = JSON.parse(reader.result);
          await AI.loadConfig(json);
          populateModelsFromConfig();
          showConfig();
          cfgView.textContent = '已从本地文件加载配置\n' + cfgView.textContent;
          cfgActions.style.display = 'none';
        } catch (err) {
          cfgView.textContent = '解析失败：' + (err?.message || err);
        }
      };
      reader.onerror = () => {
        cfgView.textContent = '读取失败：' + (reader.error?.message || 'unknown error');
      };
      reader.readAsText(file, 'utf-8');
    };

    // Chat
    const chatInput = document.getElementById('chat-input');
    const chatOut = document.getElementById('chat-output');
    document.getElementById('chat-send').onclick = async () => {
      chatOut.textContent = '请求中...';
      try {
        const res = await AI.chatCompletion({
          messages: [{ role: 'user', content: chatInput.value || '你好' }],
          model: modelSelect.value || undefined,
        });
        chatOut.textContent = JSON.stringify(res, null, 2);
      } catch (e) {
        chatOut.textContent = '失败：' + (e?.message || e);
      }
    };

    // Vision
    const vUrl = document.getElementById('vision-url');
    const vQ = document.getElementById('vision-q');
    const vOut = document.getElementById('vision-output');
    document.getElementById('vision-send').onclick = async () => {
      vOut.textContent = '请求中...';
      try {
        const res = await AI.visionDescribe({
          imageUrl: vUrl.value || 'https://ark-project.tos-cn-beijing.ivolces.com/images/view.jpeg',
          question: vQ.value || '这是哪里？',
          model: modelSelect.value || undefined,
        });
        vOut.textContent = JSON.stringify(res, null, 2);
      } catch (e) {
        vOut.textContent = '失败：' + (e?.message || e);
      }
    };

    // MockParse
    const mText = document.getElementById('mock-text');
    const mOut = document.getElementById('mock-output');
    document.getElementById('mock-run').onclick = () => {
      const parsed = AI.mockParse(mText.value || '开始拣货 300 件，优先近效期');
      mOut.textContent = JSON.stringify(parsed, null, 2);
    };
  </script>
</body>
</html>
