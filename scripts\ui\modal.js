(function(){
  const uiModal = { overlay:null, title:null, msg:null, input:null, ok:null, cancel:null, closeBtn:null };
  function ensureModalRefs(){
    if (!uiModal.overlay) uiModal.overlay = document.getElementById('ui-modal-overlay');
    if (!uiModal.title) uiModal.title = document.getElementById('ui-modal-title');
    if (!uiModal.msg) uiModal.msg = document.getElementById('ui-modal-message');
    if (!uiModal.input) uiModal.input = document.getElementById('ui-modal-input');
    if (!uiModal.ok) uiModal.ok = document.getElementById('ui-modal-ok');
    if (!uiModal.cancel) uiModal.cancel = document.getElementById('ui-modal-cancel');
    if (!uiModal.closeBtn) uiModal.closeBtn = document.getElementById('ui-modal-close');
  }

  window.openModal = function({ title='提示', message='', mode='confirm', placeholder='', okText='确定', cancelText='取消', okType='primary', defaultValue='' }){
    return new Promise((resolve) => {
      ensureModalRefs();
      if (!uiModal.overlay) return resolve(null);

      uiModal.title.textContent = title;
      uiModal.msg.textContent = message;
      const isPrompt = mode === 'prompt';
      uiModal.input.style.display = isPrompt ? 'block' : 'none';
      uiModal.input.value = isPrompt ? (defaultValue || '') : '';
      uiModal.input.placeholder = placeholder || '';
      uiModal.ok.textContent = okText;
      uiModal.cancel.textContent = cancelText;

      uiModal.ok.classList.remove('btn-primary','btn-danger');
      uiModal.ok.classList.add(okType === 'danger' ? 'btn-danger' : 'btn-primary');

      const cleanup = () => {
        document.removeEventListener('keydown', keyHandler);
        uiModal.overlay.removeEventListener('click', overlayClick);
        uiModal.input.removeEventListener('input', autoResize);
        uiModal.input.removeEventListener('keydown', inputKeyHandler);
        uiModal.ok.onclick = null;
        uiModal.cancel.onclick = null;
        uiModal.closeBtn.onclick = null;
        uiModal.overlay.classList.remove('open');
        uiModal.overlay.setAttribute('aria-hidden','true');
      };
      const overlayClick = (e) => { if (e.target === uiModal.overlay) { cleanup(); resolve(null); } };
      const keyHandler = (e) => { if (e.key === 'Escape') { cleanup(); resolve(null); } };
      const inputKeyHandler = (e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); const v = uiModal.input.value.trim(); cleanup(); resolve(v || ''); } };
      const autoResize = () => { uiModal.input.style.height = 'auto'; uiModal.input.style.height = Math.min(uiModal.input.scrollHeight, 240) + 'px'; };

      uiModal.ok.onclick = () => { if (isPrompt) { const v = uiModal.input.value.trim(); cleanup(); resolve(v || ''); } else { cleanup(); resolve(true); } };
      uiModal.cancel.onclick = () => { cleanup(); resolve(null); };
      uiModal.closeBtn.onclick = () => { cleanup(); resolve(null); };

      document.addEventListener('keydown', keyHandler);
      uiModal.overlay.addEventListener('click', overlayClick);
      uiModal.overlay.classList.add('open');
      uiModal.overlay.setAttribute('aria-hidden','false');
      setTimeout(() => { if (isPrompt) { uiModal.input.focus(); autoResize(); } else { uiModal.ok.focus(); } }, 10);
      if (isPrompt) {
        uiModal.input.addEventListener('input', autoResize);
        uiModal.input.addEventListener('keydown', inputKeyHandler);
      }
    });
  };
})();
