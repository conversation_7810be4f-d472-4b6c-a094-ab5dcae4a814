/* pages/help/help.css - Help 页面级样式（逐步从 help.html 迁移） */

/* 过渡占位：随着重构推进，页面级样式将从 help.html 内联 <style> 迁移至此。
   本文件当前仅作为结构化入口存在，不改变现有视觉表现。 */

/* 例：页面加载渐显（与原有保持一致；如与内联重复不会影响显示） */
/* body.loaded { opacity: 1; } */



/* FAQ 弹窗样式（自 help.html 迁移） */
.faq-modal-overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.35); display: none; align-items: center; justify-content: center; z-index: 1000; }
.faq-modal { width: min(560px, 92vw); background: #fff; border-radius: 12px; border: 1px solid rgba(0,0,0,0.08); box-shadow: 0 20px 40px rgba(0,0,0,0.2); overflow: hidden; }
.faq-modal-header { display: flex; align-items: center; justify-content: space-between; padding: 12px 16px; border-bottom: 1px solid rgba(0,0,0,0.06); font-weight: 600; color: #2d3748; }
.faq-close-btn { background: transparent; border: none; width: 28px; height: 28px; border-radius: 6px; cursor: pointer; color: #718096; }
.faq-close-btn:hover { background: rgba(0,0,0,0.06); }
.faq-modal-list { padding: 10px; max-height: 360px; overflow: auto; display: grid; grid-template-columns: 1fr; gap: 8px; }
.faq-item { display: flex; align-items: center; gap: 8px; padding: 10px 12px; border-radius: 8px; border: 1px solid rgba(0,0,0,0.06); background: #f8fafc; cursor: pointer; transition: all 0.2s ease; text-align: left; }
.faq-item:hover { background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color: #fff; border-color: transparent; transform: translateY(-1px); }


/* ==== Moved from help.html <style> (part 1) ==== */
/* 现代化 AI 助手界面设计（绿主题 + 无边距 + 输入建议） */
/* tokens moved to public/css/tokens.css */

/* 基础样式 */
/* 说明：整体去除紫色背景与外间距，使用绿色系与全出血布局 */
* { box-sizing: border-box; }
body { margin:0; padding:0; height:100vh; overflow:hidden; font-family:'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: var(--bg); zoom:.75; opacity:0; transition: opacity .3s ease; }
body.loaded { opacity:1; }
.app-container { display:flex; height: calc(100vh / 0.75); background: var(--bg); }
/* 主工作区容器（卡片化） */
.chat-container { display:flex; flex-direction:column; flex:1; height:100%; background:transparent; border:none; border-radius:0; box-shadow:none; overflow:hidden; }
/* 顶部 Header（轻玻璃 + 渐变边） */
.chat-header { display:flex; align-items:center; justify-content:space-between; padding:12px 16px; background:#ffffff; border-bottom:1px solid var(--border); }
.header-left{ display:flex; align-items:center; gap:10px; }
.ai-avatar{ width:32px; height:32px; border-radius:10px; background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); display:flex; align-items:center; justify-content:center; color:#fff; box-shadow:0 6px 14px rgba(57,181,74,0.25); }
.header-info h1{ margin:0; font-size:16px; font-weight:700; color:#0f172a; }
.header-info p{ margin:0; font-size:11px; color:#64748b; }
/* 统一：使用深色标题与次要文字，避免在浅色背景下“看不见” */
.header-info h1{ margin:0; font-size:16px; font-weight:700; color:#0f172a; line-height:1.25; }
.header-info p{ margin:2px 0 0 0; font-size:12px; color:#64748b; font-weight:400; }
.chat-status{ display:flex; align-items:center; gap:6px; background:#F1F5F9; padding:4px 10px; border-radius:12px; border:1px solid #E5E7EB; color:#334155; backdrop-filter:none; }
.status-dot{ width:6px; height:6px; border-radius:50%; background:#10b981; animation:pulse 2s infinite; }
/* 健康状态颜色 */
.status-online .status-dot{ background:#10b981; }
.status-busy .status-dot{ background:#f59e0b; }
.status-error .status-dot{ background:#ef4444; }
.status-offline .status-dot{ background:#9ca3af; }
.status-online .status-text, .status-busy .status-text, .status-error .status-text, .status-offline .status-text{ color:#334155; }
/* Header 重命名样式 */
#header-conv-title.editing{ outline:2px solid rgba(255,255,255,0.5); border-radius:4px; }
.header-title-row{ display:flex; align-items:center; gap:6px; }
.title-edit-btn{ border:none; background:rgba(255,255,255,0.14); color:#e5e7eb; width:20px; height:20px; border-radius:4px; cursor:pointer; font-size:12px; display:flex; align-items:center; justify-content:center; }
.title-edit-btn:hover{ background:rgba(255,255,255,0.24); }
#header-conv-title[contenteditable="true"]{ outline:2px solid rgba(255,255,255,0.5); border-radius:4px; padding:0 4px; }
.status-text{ color:#334155; font-size:11px; font-weight:500; }
@keyframes pulse{ 0%,100%{opacity:1;} 50%{opacity:.5;} }
/* 消息区 */
.chat-messages{ flex:1; overflow-y:auto; padding:16px; display:flex; flex-direction:column; gap:12px; background:transparent; position:relative; }
.message{ display:flex; gap:10px; animation: messageSlide .3s ease-out; align-items:flex-start; width:auto; max-width:100%; }
.message.user{ flex-direction:row; justify-content:flex-end; margin-left:0; }
.message.ai{ margin-right:0; gap:0; }
.message.ai .message-avatar{ display:none; }
.message.ai .message-content{ margin-left:0; }
.message-avatar{ width:28px; height:28px; border-radius:8px; display:flex; align-items:center; justify-content:center; font-size:14px; flex-shrink:0; box-shadow:0 2px 6px rgba(0,0,0,0.1); }
.message.ai .message-avatar{ background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color:#fff; }
.message.user .message-avatar{ background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%); color:#fff; }
.message-content{ padding:10px 14px; border-radius:12px; font-size:13px; line-height:1.6; white-space:pre-wrap; position:relative; box-shadow:none; width:fit-content; max-width:92%; }
.message.ai .message-content{ background:#dee3e3; color:var(--text); border:1px solid var(--border); border-radius:10px 12px 12px 12px; box-shadow:none; max-width:70%; margin-left:0; margin-right:0; display:inline-block; position:relative; transition: border-color .2s ease, background .2s ease; }
.recognized-tag{ display:inline-block; background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color:#fff; font-size:10px; line-height:1; padding:4px 6px; border-radius:6px; font-weight:500; margin-bottom:6px; box-shadow:0 2px 4px rgba(0,0,0,0.12); }
.message.user .message-content{ background:#78eb58; color:var(--text); border:1px solid rgba(165,205,57,0.28); border-radius:12px 10px 12px 12px; box-shadow:none; max-width:70%; align-self:flex-end; margin-left:auto; display:inline-block; padding:8px 12px; transition:border-color .2s ease, background .2s ease; }
.message.user .message-time{ color:#666666; }
.message.user .message-avatar{ display:none; }
.chat-messages{ padding:16px 12px; }
.message-time{ font-size:10px; color:rgba(0,0,0,0.4); margin-top:4px; text-align:right; }
.message.user .message-time{ text-align:left; }
.message-footer{ display:flex; align-items:center; gap:8px; margin-top:6px; }
.message-footer .message-time{ margin-top:0; text-align:left; }
.message-tools{ margin-left:auto; display:flex; align-items:center; gap:6px; }
.copy-btn{ width:22px; height:22px; border-radius:6px; border:0 solid rgba(0,0,0,0.08); background:#dee3e3; color:#374151; font-size:12px; display:flex; align-items:center; justify-content:center; cursor:pointer; transition:.2s background, .2s border-color, .2s color; }
.copy-btn:hover{ background:#FFFFFF; }
.copy-btn.copied{ background:#e0f2f1; border-color:#10b981; color:#065f46; }
.toast-container{ position:fixed; right:16px; bottom:16px; z-index:4000; display:flex; flex-direction:column; gap:8px; }
.toast{ min-width:180px; max-width:360px; padding:10px 12px; border-radius:10px; border:1px solid #E5E7EB; background:#FFFFFF; box-shadow:0 10px 24px rgba(2,6,23,0.12); color:#0f172a; font-size:12px; display:flex; align-items:center; gap:8px; animation: toastIn .14s ease; }
.toast.success{ border-color:rgba(16,185,129,0.35); background:#ECFDF5; color:#065f46; }
.toast.error{ border-color:rgba(239,68,68,0.35); background:#FEF2F2; color:#7f1d1d; }
.toast .icon{ font-size:14px; }
@keyframes toastIn{ from{opacity:0; transform:translateY(6px);} to{opacity:1; transform:translateY(0);} }
.message-content a{ color:var(--link); text-decoration:none; border-bottom:1px dashed rgba(14,165,233,0.35); }
.message-content a:hover{ color:var(--link-hover); border-bottom-color:rgba(2,132,199,0.55); }
.message-content code{ background:#F1F5F9; color:#0f172a; padding:1px 4px; border-radius:4px; font-family:ui-monospace, SFMono-Regular, Menlo, monospace; font-size:12px; }
.message-content pre{ background:#0f172a; color:#e5e7eb; padding:10px 12px; border-radius:10px; overflow:auto; font-size:12px; }
.message-content pre code{ background:transparent; color:inherit; padding:0; }
.send-btn{ box-shadow:0 6px 16px rgba(57,181,74,0.22); }
.send-btn:active{ transform: translateY(1px) scale(0.98); }
.typing-indicator{ display:flex; gap:4px; padding:12px 0; align-items:center; }
.typing-dot{ width:8px; height:8px; border-radius:50%; background:#cbd5e0; animation: typing 1.4s infinite; }
.typing-dot:nth-child(2){ animation-delay:.2s; }
.typing-dot:nth-child(3){ animation-delay:.4s; }
.quick-actions{ padding:12px 16px; background:#fff; border-top:1px solid rgba(0,0,0,0.06); }
.quick-actions-title{ font-size:12px; color:#4a5568; margin-bottom:8px; font-weight:600; display:flex; align-items:center; gap:6px; }
.quick-buttons{ display:grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); gap:8px; }
.quick-btn{ background:linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border:1px solid rgba(0,0,0,0.08); border-radius:8px; padding:8px 12px; font-size:12px; color:#2d3748; cursor:pointer; transition:all .2s ease; display:flex; align-items:center; gap:6px; font-weight:500; text-align:left; }
.quick-btn:hover{ background:linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color:#fff; border-color:transparent; transform: translateY(-1px); box-shadow:0 4px 12px rgba(57,181,74,0.3); }
.chat-input-area{ padding:12px 16px 16px; background:transparent; border-top:none; }
.input-container{ display:flex; flex-direction:column; gap:8px; }
.input-field{ flex:1; min-height:72px; max-height:300px; padding:10px 0; border:none; background:transparent; color:#2d3748; resize:none; font-size:15px; line-height:1.6; outline:none; font-family:inherit; caret-color: var(--primary-dark); }
.input-field::placeholder{ color:#a0aec0; }
.input-field::selection{ background: rgba(165,205,57,0.22); }
.send-btn{ width:32px; height:32px; border-radius:8px; background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color:#fff; border:none; cursor:pointer; display:flex; align-items:center; justify-content:center; transition: all .3s cubic-bezier(0.25, 0.46, 0.45, 0.94); box-shadow:0 2px 8px rgba(57,181,74,0.2); }
.send-btn:hover{ transform: translateY(-1px); box-shadow:0 4px 12px rgba(57,181,74,0.3); }
.send-btn:active{ transform: translateY(0); }
.send-btn:disabled{ background:#e2e8f0; color:#a0aec0; cursor:not-allowed; transform:none; box-shadow:none; }
.toolbar-left, .toolbar-right{ display:flex; align-items:center; gap:6px; }
.toolbar-divider{ width:1px; height:18px; background: rgba(0,0,0,0.1); margin:0 8px; }
.tool-btn{ width:24px; height:24px; border-radius:6px; border:none; background:transparent; color:#718096; cursor:pointer; display:flex; align-items:center; justify-content:center; transition: all .2s ease; font-size:12px; }
.tool-btn:hover{ background: rgba(165,205,57,0.1); color: var(--primary-dark); }
.tool-btn.active{ background: rgba(165,205,57,0.15); color: var(--primary-dark); }
.model-selector{ height:24px; border-radius:6px; border:1px solid rgba(0,0,0,0.08); background:#fff; color:#718096; padding:0 6px; font-size:10px; cursor:pointer; transition: all .2s ease; outline:none; min-width:70px; }
.ai-priority-wrapper{ display:flex; align-items:center; gap:4px; margin-left:6px; font-size:10px; color:#4a5568; user-select:none; }
.ai-toggle{ position:relative; width:30px; height:16px; border-radius:16px; background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); cursor:pointer; transition: background .25s ease; }
.ai-toggle.off{ background:#cbd5e0; }
.ai-toggle-knob{ position:absolute; top:2px; left:2px; width:12px; height:12px; border-radius:50%; background:#fff; box-shadow:0 1px 2px rgba(0,0,0,0.25); transition: transform .25s ease; }
.ai-toggle.on .ai-toggle-knob{ transform: translateX(14px); }
.model-selector:hover{ border-color: var(--primary); color: var(--primary-dark); }
.model-selector:focus{ border-color: var(--primary); box-shadow: 0 0 0 2px rgba(165,205,57,0.1); }
/* 增强输入框样式 - 三层结构 */
.enhanced-input-container{ background:#fff; border:1px solid #E2E8F0; border-radius:16px; transition: background .2s ease; position:relative; }
.enhanced-input-container:focus-within{ border-color: rgba(165,205,57,0.6); background:#fff; box-shadow: 0 0 0 3px rgba(165,205,57,0.12); }
.input-suggestions-area{ padding:8px 12px 4px; border-bottom:1px solid rgba(0,0,0,0.06); }
.input-text-area{ padding:6px 12px; }
.input-bottom-toolbar{ display:flex; align-items:center; justify-content:space-between; padding:8px 12px; border-top:1px solid rgba(0,0,0,0.06); }
.kbd-hint{ font-size:10px; color:#94a3b8; margin-right:8px; user-select:none; }
@media (max-width:768px){ .kbd-hint{ display:none; } }
.suggestions-title{ font-size:11px; color:#718096; margin-bottom:6px; font-weight:500; }
/* 常见问题弹窗样式已迁移至 pages/help/help.css */
.suggestion-chips{ display:flex; flex-wrap:wrap; gap:6px; }
.suggestion-chip{ background: rgba(165,205,57,0.1); border:1px solid rgba(165,205,57,0.2); border-radius:12px; padding:4px 8px; font-size:11px; color: var(--primary-dark); cursor:pointer; transition: all .2s ease; display:flex; align-items:center; gap:4px; font-weight:500; }
.suggestion-chip:hover{ background: var(--primary); color:#fff; border-color: var(--primary); transform: translateY(-1px); }
/* 中部：文字输入区域 */
.input-text-area{ padding:12px; display:flex; align-items:flex-start; gap:10px; min-height:96px; }
/* 下部：工具栏区域 */
.input-bottom-toolbar{ padding:6px 12px 8px; border-top:none; display:flex; align-items:center; justify-content:space-between; }
.welcome-message{ text-align:center; padding:40px 20px; color:#4a5568; }
.welcome-icon{ font-size:32px; margin-bottom:8px; }
.welcome-title{ font-size:18px; font-weight:600; margin-bottom:4px; color:#2d3748; }
.welcome-subtitle{ font-size:13px; color:#718096; margin-bottom:16px; }
.welcome-features{ display:grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap:8px; margin-top:12px; }
.feature-item{ background:#fff; padding:12px; border-radius:8px; border:1px solid rgba(0,0,0,0.06); text-align:left; }
.feature-icon{ font-size:16px; margin-bottom:4px; }
.feature-title{ font-weight:600; margin-bottom:2px; color:#2d3748; font-size:12px; }
.feature-desc{ font-size:11px; color:#718096; }
@keyframes messageSlide{ from{ opacity:0; transform: translateY(20px) scale(.95);} to{ opacity:1; transform: translateY(0) scale(1);} }
@keyframes typing{ 0%,60%,100%{ transform:translateY(0); opacity:.4;} 30%{ transform:translateY(-8px); opacity:1; } }
/* 响应式设计 */
@media (max-width:768px){ .app-container{ background:#fff; } .chat-container{ margin:0; border-radius:0; height:100vh; } .chat-header{ padding:12px 16px; border-radius:0; } .header-left{ gap:8px; } .ai-avatar{ width:28px; height:28px; } .header-info h1{ font-size:14px; } .chat-messages{ padding:20px 12px; } .message.user{ margin-left:0; } .message.ai{ margin-right:0; } .message-content{ max-width:85%; } .quick-actions{ padding:20px 16px; } .quick-buttons{ grid-template-columns:1fr; gap:8px; } .chat-input-area{ padding:12px; } .input-wrapper{ padding:6px 8px; } .input-toolbar{ padding:0 2px; } .toolbar-left{ gap:6px; } .toolbar-right{ gap:6px; } .tool-btn{ width:24px; height:24px; font-size:12px; } .model-selector{ min-width:70px; font-size:10px; } }
@media (max-width:480px){ .header-info h1{ font-size:18px; } .header-info p{ font-size:12px; } .chat-status{ padding:6px 12px; } .status-text{ font-size:12px; } }
/* 统一 UI 弹窗（确认/输入） */
.ui-modal-overlay{ position:fixed; inset:0; background: rgba(0,0,0,0.45); display:none; align-items:center; justify-content:center; z-index:3000; }
.ui-modal-overlay.open{ display:flex; animation: fadeIn .12s ease; }
@keyframes fadeIn{ from{opacity:0} to{opacity:1} }
.ui-modal{ width:auto; min-width:280px; max-width:420px; background:#0f172a; color:#e5e7eb; border:1px solid rgba(255,255,255,0.08); border-radius:12px; box-shadow:0 18px 42px -12px rgba(0,0,0,0.65); overflow:hidden; }
.ui-modal-header{ display:flex; align-items:center; justify-content:space-between; padding:10px 12px; background: linear-gradient(180deg, rgba(255,255,255,0.04), transparent); font-weight:600; font-size:13px; }
.ui-modal-close{ background:transparent; border:none; color:#9ca3af; width:24px; height:24px; cursor:pointer; border-radius:6px; }
.ui-modal-close:hover{ background: rgba(255,255,255,0.06); color:#e5e7eb; }
.ui-modal-body{ padding:12px; font-size:13px; line-height:1.5; }
.ui-modal-message{ margin:0 0 10px 0; color:#cbd5e1; }
.ui-modal-input{ width:100%; box-sizing:border-box; padding:8px 10px; border:1px solid rgba(255,255,255,0.10); border-radius:8px; background: rgba(255,255,255,0.04); color:#e5e7eb; outline:none; font-size:13px; line-height:1.5; min-height:36px; max-height:240px; resize:none; }
.ui-modal-input::placeholder{ color:#94a3b8; }
.ui-modal-footer{ display:flex; gap:8px; justify-content:flex-end; padding:10px 12px 12px; }
.btn{ height:30px; padding:0 12px; font-size:12px; border:none; border-radius:8px; cursor:pointer; display:inline-flex; align-items:center; justify-content:center; }
.btn-primary{ background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color:#fff; }
.btn-primary:hover{ filter:brightness(1.05); }
.btn-secondary{ background: rgba(255,255,255,0.08); color:#e5e7eb; border:1px solid rgba(255,255,255,0.10); }
.btn-secondary:hover{ background: rgba(255,255,255,0.12); }
.btn-danger{ background: rgba(239, 68, 68, 0.12); color:#ef4444; border:1px solid rgba(239, 68, 68, 0.35); }
.btn-danger:hover{ background: rgba(239, 68, 68, 0.18); }


/* ==== Inline style收敛（从 help.html 迁移） ==== */
/* 侧栏顶部行布局 */
.sidebar-top-row{ display:flex; align-items:center; justify-content:space-between; gap:6px; }
/* 品牌区 */
.brand-row{ display:flex; align-items:center; gap:6px; }
.brand-text-col{ display:flex; flex-direction:column; }
.brand-title{ font-size:13px; font-weight:700; letter-spacing:.5px; color:#111827; }
.brand-subtitle{ font-size:10px; color:#6b7280; }
/* 侧栏中部容器（增强为列布局并可滚动区自适应） */
.sidebar-mid{ display:flex; flex-direction:column; gap:10px; flex:1; min-height:0; }
/* 侧栏小标题与头部 */
.sidebar-header{ padding:0 2px; }
.sidebar-section-title{ font-size:11px; font-weight:600; letter-spacing:.5px; opacity:.85; }
/* 侧栏底部与设置按钮/版本号 */
.sidebar-bottom{ padding-top:8px; border-top:1px solid #E5E7EB; display:flex; flex-direction:column; gap:8px; }
#settings-btn{ background:#F9FAFB; border:1px solid #E5E7EB; color:#374151; height:34px; border-radius:10px; display:flex; align-items:center; justify-content:center; font-size:12px; cursor:pointer; gap:6px; }
.version-label{ font-size:10px; color:#6b7280; text-align:center; }
/* Header右侧信息行 */
.header-meta-row{ display:flex; align-items:center; gap:14px; font-size:11px; }
#header-updated-time{ opacity:.65; color:#475569; }
/* 快捷区域默认隐藏（页面脚本按需控制显示） */
.quick-actions{ display:none; }
/* 通用隐藏工具类 */
.hidden{ display:none !important; }
