<a name="top"></a>

# AI WMS / AI 运维助手（前端原型）

一个零构建依赖、纯 HTML + CSS + 原生 JavaScript 的 AI 驱动 WMS（仓库管理 / 运维支持）交互与功能验证原型。目标：探索 “自然语言 + 规则兜底 + 可视化反馈 + 轻量持久化” 在仓库业务与运维支持场景中的最小可行形态。

> 本项目当前定位为 **快速验证与交互演示**：前端直连模型密钥、示例知识库、简化安全策略、内存/本地浏览器存储。切勿直接用于生产。

---

## ✨ 核心特性

### 1. AI 交互 / 运维助手（`pages/help/help.html`）
- 多轮对话：本地会话存储 +（可选）SQLite (sql.js + IndexedDB 持久化字节包)
- 模型切换：基于配置文件或内置 `models` 列表动态渲染下拉框
- “AI 优先” / “规则优先” 双模：关闭 AI 优先开关时先做本地 FAQ 分类命中（降低 Token 消耗、提升响应速度）
- FAQ 本地分类器：基于关键字 + 轻权重打分 + 场景冲突修正（扫码 + 入库缺单场景）
- Typing 动画、复制按钮、Toast 反馈、时间标签与标题自动生成
- 双击标题内联重命名（`components/header/header.js`）

### 2. 通用 AI SDK（`ai.js`）
- 兼容 Ark(OpenAI 风格) Chat Completions 与多模态（图片 + 文本）
- 支持：`direct`（直连）、`mock`（离线演示）、`proxy`（预留后端代理模式）
- 超时控制 + 流式（stream=true 预留 Reader）
- `mockParse(text)` 提供与旧 demo 中一致的轻量意图解析（出库 / 入库 / 退货 / 流程）
- 统一 `loadConfig`：支持 URL JSON 或内联对象（便于环境切换）

### 3. 会话管理（`components/conversation-list/*` + `help.js` 第 3 段）
- 多会话列表：收藏 / 重命名 / 删除 / 时间分组（今天 / 昨天 / 周内 / 日期）
- 标题自适应：首条 AI 回复回填标题，或用户手动编辑
- 本地存储裁剪策略：最大会话数 / 每会话消息数 / 单消息长度 / 总字节上限
- SQLite 镜像：使用 sql.js 将结构化消息写入虚拟 DB，再增量导出二进制并存 IndexedDB（便于以后导出/迁移/统计）

### 4. UI / UX
- 纯 CSS Token（`public/css/tokens.css`） + 主题（`styles/theme-light.css` / `theme-dark.css` 预留）
- 渐进增强：在缺失模型配置时仍可 fallback 到本地 FAQ 兜底响应
- 无框架：便于快速嵌入到任意已有系统 iframe / webview / portal

### 5. 可扩展点
- 后端代理接入（隐藏密钥、统一审计、配额节流）
- FAQ 分类器可替换为向量检索 / RAG（embedding + 近邻召回 + 模型总结）
- 会话 & FAQ 数据导出为标准 JSON / SQL Dump

---

## 📂 目录概览

```
├── ai-config.json              # AI SDK 运行配置（当前示例直连，生产请移除密钥）
├── ai.js                       # 通用前端 AI 封装
├── demo.html                   # 旧版交互/意图解析原型 (动作卡 / 库存模拟)
├── test.html                   # AI 能力测试台（直连 / mock / 多模态）
├── qr.html                     # （预留）二维码/图像相关实验页
├── pages/
│   └── help/                   # 运维助手主页面
│       ├── help.html
│       ├── help.js             # 页面脚本（拆三段：交互/状态/会话+SQLite）
│       └── help.css
├── components/
│   ├── conversation-list/      # 会话侧栏组件
│   │   ├── conversation.js
│   │   └── conversation.css
│   └── header/
│       └── header.js           # 标题 inline rename 逻辑
├── scripts/
│   ├── storage/sqlitePersistence.js   # sql.js 导出/回灌 + IndexedDB
│   └── ui/modal.js                     # 通用模态框 (openModal)
├── public/css/tokens.css       # 设计 Token（颜色/间距/圆角等）
├── styles/                     # 主题 + 基础样式
│   ├── base.css
│   ├── theme-light.css
│   └── theme-dark.css (可选扩展)
└── aiwms/README.md             # 子目录简述（可合并或保留）
```

---

## 🚀 快速开始

### 方式 A：直接打开（演示 & 离线）
1. 双击 `pages/help/help.html` 或 `demo.html`
2. 如需 mock：在控制台执行 `AI.configure({ mode: 'mock' })`
3. 若浏览器阻止 `fetch('./ai-config.json')`（file:// 场景），自动 fallback 到内置配置或手动调用：
   ```js
   AI.loadConfig({ mode:'mock' });
   ```

### 方式 B：本地静态服务器（推荐）
在项目根目录启动任意 http server（以下示例任选其一）：

```powershell
# VS Code Live Server / 或者：
python -m http.server 8000 ; # 需要已安装 Python
npx serve . ; # Node 环境
```
然后访问：`http://localhost:8000/pages/help/help.html`

### 方式 C：嵌入既有系统
将 `ai.js + pages/help/* + components/* + styles/*` 拷入宿主应用的静态资源目录，按相对路径修正引用即可。

---

## 🔐 配置与密钥安全
当前示例仓库包含明文 `apiKey` 仅用于演示，务必：
1. 提交代码前移除真实密钥
2. 生产改为后端代理：前端 -> `/api/ai/chat` -> 服务端携带 Ark Key 调用真实模型
3. 在 `ai.js` 中扩展 `proxy` 分支：`fetch('/api/ai/chat',{ body: JSON.stringify({ messages }) })`

---

## 🧠 本地 FAQ 分类器策略
- 关键字累加打分 + 文本长度归一化
- 特殊冲突规则：扫码 + 入库缺单 → 强制倾向 “入库订单缺失” 类别
- 阈值（`CONF_THRESHOLD`）下限回退到大模型回答
→ 产出更快首响应；降低 Token 成本；适合高频、固定问句场景

---

## 💾 数据持久化与存储策略
| 层次 | 介质 | 作用 | 裁剪策略 |
| ---- | ---- | ---- | -------- |
| 内存 | JS 对象 | 当前运行态 | 刷新即失 |
| localStorage | JSON 串 | 快速恢复最近会话 | 限制会话数/消息数/长度/总字节 |
| IndexedDB + sql.js | 二进制 DB | 结构化查询 + 扩展统计 | 节流导出 (`throttleMs`) |

其它：`scheduleSQLiteSaveSafely()` 节流调度；刷新前尝试 `flushNow()`。

---

## 🧩 关键模块速览
| 文件 | 说明 |
| ---- | ---- |
| `ai.js` | 通用 AI SDK：配置 / chatCompletion / visionDescribe / mockParse |
| `conversation.js` | 会话列表渲染 + 收藏/重命名/删除 + 上下文切换 |
| `help.js` | 分段：消息交互 + 模型/状态 + 持久化/SQLite 初始化 |
| `sqlitePersistence.js` | IndexedDB 持久化二进制快照 + 节流写入 |
| `header.js` | 标题内联编辑（contenteditable） |
| `modal.js` | 通用模态交互（Promise 化） |

---

## 🛠 开发指南
1. 新增 FAQ：编辑 `help.js` 第一段里的 `FAQ_KB` 常量
2. 新增模型：在 `ai-config.json` 的 `models` 数组加入 ID；或运行时 `AI.configure({ models:[ ... ] })`
3. 加入代理模式：实现后端接口后在 `ai.js` 的 `proxy` 分支填充 fetch 逻辑
4. 接入向量检索：在发送前管线中增加 embedding + 相似度召回，合并上下文再调用 `chatCompletion`
5. 添加主题：复制 `theme-light.css` → `theme-dark.css` 并在 HTML 里切换 `#theme-css` 的 href

### 代码风格建议
- 保持零依赖：避免引入打包工具 / 框架导致调试复杂化
- 模块内聚：与某 UI 区块强相关的逻辑优先放在对应组件目录
- 命名：`render* / load* / save* / schedule* / refresh*` 职责单一

---

## 🔭 路线图 (Roadmap)
- [ ] Proxy 模式后端参考实现（Node / Koa）
- [ ] 支持 SSE/流式增量渲染
- [ ] FAQ → 向量检索升级 (Embedding + Top-k + Rerank)
- [ ] 导出 / 导入会话（JSON / SQL Dump）
- [ ] 用户登录与权限隔离（多租户）
- [ ] 统一操作审计与埋点回放
- [ ] 单元测试（Vitest / Playwright 可选）
- [ ] Dark Theme & 自适应主题切换
- [ ] PWA 打包（离线缓存 FAQ + UI 资源）

---

## 🔄 与 `demo.html` 的关系
`demo.html` 代表更偏 “业务流程 + 意图解析 + 库存动作模拟” 的早期原型；`pages/help/help.html` 聚焦在 “运维 / 问题解答 / 对话增强”。后续可以：
1. 抽离共用 UI（输入框 / toast / model-selector）
2. 合并为统一入口（Tab：业务执行 / 运维支持）

---

## 🧪 最小自测清单
| 动作 | 期望 |
| ---- | ---- |
| 输入 “二维码扫描不出来” | 命中本地 FAQ（AI 优先关闭）|
| 输入 “出库冻结怎么办” | 命中出库 FAQ |
| 切换模型下拉 | 状态文案 2 秒内变更后恢复 |
| 新建对话 → 输入消息 | 列表出现新会话，标题被首条消息截断生成 |
| 刷新页面 | 上次会话自动恢复（localStorage） |
| 多条消息后刷新 | IndexedDB 中存在 sqlite 二进制（可在 DevTools 检查） |

---

## 📜 许可（License）
当前未指定开源许可证，默认保留全部权利（All Rights Reserved）。如需开源请补充 LICENSE 文件并在此声明。

---

## 🤝 贡献
欢迎通过 Issue / PR 提出：功能建议、UI 优化、性能改进、安全加固思路。提交前请：
1. 遵循零依赖约束
2. 附上复现步骤或截图
3. 尽量保持中文注释简洁、英文命名清晰

---

## 📬 联系 & Feedback
如果你在企业内部验证该原型：
- 建议先实现内网代理再接入真实密钥
- 可以添加操作审计（fetch 包装 + 事件上报）
- 结合向量检索替换 FAQ 模块

> 欢迎基于该原型二次开发；请勿将示例密钥 / 内部业务数据提交到公共仓库。

---

返回顶部： [⬆️ Back to Top](#top)

